<?php
/**
 * Award Force-specific routing rules and filter definitions.
 */

// Normal application routes that support the main Award Force app

use AwardForce\Http\Controllers\NewDashboardController;
use AwardForce\Http\Middleware\Authenticate;
use AwardForce\Http\Middleware\BroadcastRecipient;
use AwardForce\Http\Middleware\ExpectJson;
use AwardForce\Http\Middleware\RequireAccount;
use AwardForce\Http\Middleware\RequireMembership;
use AwardForce\Http\Middleware\StorePostParameters;
use AwardForce\Http\Middleware\XUACompatible;
use AwardForce\Modules\Authentication\Saml\Middleware\Saml;
use AwardForce\Modules\Chapters\Middleware\RedirectIfNotMultiChapter;
use AwardForce\Modules\Ecommerce\Cart\Middleware\CartIsReadyForOnsitePayment;
use AwardForce\Modules\Ecommerce\Cart\Middleware\CheckoutDiscountValidity;
use AwardForce\Modules\Ecommerce\Cart\Middleware\DiscountValidity;
use AwardForce\Modules\Ecommerce\Cart\Middleware\EmptyCart;
use AwardForce\Modules\Ecommerce\Cart\Middleware\PaymentConfigurationCheck;
use AwardForce\Modules\Entries\Middleware\DuplicateScanInProgress;
use AwardForce\Modules\Entries\Middleware\LockDuplicateActions;
use AwardForce\Modules\Forms\Collaboration\Middleware\ConditionalOutdated;
use AwardForce\Modules\Forms\Forms\Http\Middleware\FormRoleAccess;
use AwardForce\Modules\Galleries\Services\ContextGalleries;
use AwardForce\Modules\Identity\Users\Middleware\BlockUser;
use AwardForce\Modules\Identity\Users\Middleware\GuestUser;
use AwardForce\Modules\Identity\Users\Middleware\ReviewStageGuestUser;
use AwardForce\Modules\Identity\Users\Middleware\ThrottleMobileUpdate;
use AwardForce\Modules\Integrations\Middleware\CopyleaksEnabled;
use AwardForce\Modules\Integrations\Middleware\SalesforceEnabled;
use AwardForce\Modules\Judging\Middleware\AllowedBulkDownload;
use AwardForce\Modules\Judging\Middleware\AllowedPreview;
use AwardForce\Modules\NewDashboard\Middleware\DashboardEnabled;
use AwardForce\Modules\NewDashboard\Middleware\DashboardExists;
use Illuminate\Support\Facades\Route;
use Platform\Menu\Context\ContextMenuService;

if (! app()->environment('production')) {
    // Installation
    Route::group(['middleware' => 'awardforce.install'], function () {
        Route::get('install', [
            'as' => 'install.form',
            'uses' => 'InstallationController@getInstall',
        ]);

        Route::post('install', [
            'as' => 'install.form.submit',
            'uses' => 'InstallationController@postInstall',
        ]);
    });

    // Laravel log viewer
    Route::get(
        'logs',
        '\Rap2hpoutre\LaravelLogViewer\LogViewerController@index'
    );
}

Route::group([], function () {
    Route::group([
        'middleware' => [
            RequireAccount::class,
            'consumer',
            RequireMembership::class,
            BlockUser::class,
            'pjax',
            'csrf',
            XUACompatible::class,
            StorePostParameters::class,
            'authenticator',
            'preview-mode',
        ],
    ], function () {
        if (config('app.env') != 'production') {
            Route::get(
                'test/{any?}',
                [
                    'as' => 'test.index',
                    'uses' => 'TestController@index',
                ]
            );
        }

        Route::post('toggle-star', [
            'as' => 'toggle-star',
            'middleware' => ['auth.role'],
            'uses' => 'StarController@toggle',
        ]);
        Route::get('payment-subscription', [
            'as' => 'payment.subscription.show',
            'uses' => 'PaymentSubscriptionController@show',
        ]);
        Route::post('payment-subscription/payment-intent', [
            'as' => 'payment.subscription.payment-intent',
            'uses' => 'PaymentSubscriptionController@paymentIntent',
        ]);
        Route::post('payment-subscription', [
            'as' => 'payment.subscription.create',
            'uses' => 'PaymentSubscriptionController@create',
        ]);

        // Agreements
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('entry/judge/agreement/{scoreSet}', [
                'as' => 'judging.agreement',
                'uses' => 'JudgingController@agreement',
            ]);

            Route::post('entry/judge/agree/{scoreSet}', [
                'as' => 'judging.agree',
                'uses' => 'JudgingController@agree',
            ]);
        });

        // Assignments
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('assignment', [
                'as' => 'assignment.index',
                'middleware' => 'remembrance',
                'uses' => 'AssignmentController@index',
            ]);

            Route::get('assignment/autocomplete', [
                'as' => 'assignment.autocomplete',
                'uses' => 'AssignmentController@autocomplete',
            ]);

            Route::get('assignment/judge/{judgeId}', [
                'as' => 'assignment.judge',
                'uses' => 'AssignmentController@judge',
            ]);

            Route::get('assignment/list/{judgeId}', [
                'as' => 'assignment.list',
                'uses' => 'AssignmentController@list',
            ]);

            Route::get('assignment/new', [
                'as' => 'assignment.new',
                'uses' => 'AssignmentController@new',
            ]);

            Route::get('assignment/new-random', [
                'as' => 'assignment.new-random',
                'uses' => 'AssignmentController@newRandom',
            ]);

            Route::get('assignment/judges', [
                'as' => 'assignment.judge-search',
                'uses' => 'AssignmentController@judgeSearch',
            ]);

            Route::get('assignment/entries', [
                'as' => 'assignment.entry-search',
                'uses' => 'AssignmentController@entrySearch',
            ]);

            Route::post('assignment', [
                'as' => 'assignment.assign',
                'uses' => 'AssignmentController@assign',
            ]);

            Route::post('random-assignment', [
                'as' => 'assignment.assign-random',
                'uses' => 'AssignmentController@randomlyAssign',
            ]);

            Route::put('assignment/{assignment}/unlock', [
                'as' => 'assignment.unlock',
                'uses' => 'AssignmentController@unlock',
            ]);

            Route::delete('assignment/delete', 'AssignmentController@delete')
                ->name('assignment.delete');

            Route::post('assignment/recuse', [
                'as' => 'assignment.recuse',
                'uses' => 'AssignmentController@recuse',
            ]);

            Route::delete('assignment/unrecuse', [
                'as' => 'assignment.unrecuse',
                'uses' => 'AssignmentController@unrecuse',
            ]);

            Route::post('assignment/tag', [
                'as' => 'assignment.tag',
                'uses' => 'AssignmentController@tag',
            ]);

            Route::delete('assignment/untag', [
                'as' => 'assignment.untag',
                'uses' => 'AssignmentController@untag',
            ]);
        });

        // Api
        Route::group(
            ['middleware' => ['auth.role', 'feature:api']],
            function () {
                Route::get('api-key', [
                    'as' => 'api-key.index',
                    'middleware' => 'remembrance',
                    'uses' => 'ApiKeyController@index',
                ]);

                Route::get('api-key/generate', [
                    'as' => 'api-key.new',
                    'uses' => 'ApiKeyController@getGenerate',
                ]);

                Route::get('api-key/{apiKey}', [
                    'as' => 'api-key.edit',
                    'uses' => 'ApiKeyController@edit',
                ]);

                Route::get('api-key/{apiKey}/show', [
                    'as' => 'api-key.show',
                    'uses' => 'ApiKeyController@show',
                ]);

                Route::post('api-key/generate', [
                    'as' => 'api-key.generate',
                    'uses' => 'ApiKeyController@generate',
                ]);

                Route::put('api-key/{apiKey}', [
                    'as' => 'api-key.update',
                    'uses' => 'ApiKeyController@update',
                ]);

                Route::delete('api-key/{apiKey}', [
                    'as' => 'api-key.revoke',
                    'uses' => 'ApiKeyController@revoke',
                ]);

                // Webhooks
                Route::get('webhook', [
                    'as' => 'webhook.index',
                    'middleware' => 'remembrance',
                    'uses' => 'WebhookController@index',
                ]);

                Route::get('webhook/new', [
                    'as' => 'webhook.new',
                    'middleware' => 'remembrance',
                    'uses' => 'WebhookController@new',
                ]);

                Route::post('webhook', [
                    'as' => 'webhook.create',
                    'uses' => 'WebhookController@create',
                ]);

                Route::get('webhook/{webhook}', [
                    'as' => 'webhook.edit',
                    'uses' => 'WebhookController@edit',
                ]);

                Route::put('webhook/{webhook}', [
                    'as' => 'webhook.update',
                    'uses' => 'WebhookController@update',
                ]);

                Route::delete('webhook', [
                    'as' => 'webhook.delete',
                    'uses' => 'WebhookController@delete',
                ]);
            }
        );

        // Audit
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('audit', [
                'as' => 'audit.index',
                'middleware' => ['remembrance', 'feature:audit'],
                'uses' => 'AuditController@index',
            ]);
            Route::get('audit/autocomplete', [
                'as' => 'audit.autocomplete',
                'middleware' => ['remembrance', 'feature:audit'],
                'uses' => 'AuditController@autocomplete',
            ]);
        });

        // CLeAR
        Route::group(
            [
                'middleware' => 'authentication.exceptions',
            ],
            function () {
                Route::get('/auth/login/{requestedRole?}', [
                    'as' => 'clear.login',
                    'middleware' => [
                        'intendedRedirection',
                        \AwardForce\Http\Middleware\AddRoleRedirect::class,
                        'guest',
                        'preferredLanguage',
                    ],
                    'uses' => 'Clear\LoginController@login',
                ]);

                Route::group(['middleware' => ['intendedRedirection', 'guest', 'preferredLanguage']], function () {
                    Route::post('initiate', [
                        'as' => 'auth.initiate',
                        'uses' => 'Clear\LoginController@initiate',
                    ]);

                    Route::post('check-consent', [
                        'as' => 'auth.consent',
                        'middleware' => 'throttle:auth.consent',
                        'uses' => 'Clear\LoginController@checkConsent',
                    ]);

                    Route::get('authenticate', [
                        'as' => 'password.login',
                        'middleware' => ['isTempConsumer'],
                        'uses' => 'Clear\LoginController@authenticate',
                    ]);

                    Route::get('/register', [
                        'as' => 'auth.register',
                        'middleware' => [
                            'userLimit',
                            'allowRegistration',
                            'verification',
                            'preferredLanguage',
                            'isTempConsumer',
                            'tempConsumerWithGlobal',
                            GuestUser::class,
                        ],
                        'uses' => 'Clear\RegistrationController@index',
                    ]);

                    Route::post('/quick-register', [
                        'as' => 'quick-register',
                        'middleware' => [
                            'userLimit',
                            'verification',
                            'isTempConsumer',
                            'tempConsumerWithGlobal',
                            'preferredLanguage',
                        ],
                        'uses' => 'Clear\RegistrationController@quickRegister',
                    ]);

                    Route::get('register/{registrationRole}', [
                        'as' => 'register.role',
                        'middleware' => ['userLimit', 'verification', 'intendedRedirection', GuestUser::class],
                        'uses' => 'Clear\RegistrationController@role',
                    ]);

                    Route::get('register/invited/{invitationToken}', [
                        'as' => 'invitation',
                        'middleware' => ['preferredLanguage', GuestUser::class],
                        'uses' => 'Clear\RegistrationController@invited',
                    ]);

                    Route::post('register/invited', [
                        'as' => 'invitation.process',
                        'uses' => 'Clear\RegistrationController@addInvited',
                    ]);
                });

                Route::get('register/complete/{registrationRole}', [
                    'as' => 'register.role.complete',
                    'middleware' => 'auth.user',
                    'uses' => 'Clear\RegistrationController@roleComplete',
                ]);

                Route::post('register', [
                    'as' => 'registration.process',
                    'middleware' => [
                        'preferredLanguage',
                        'userLimit',
                        'verification',
                        'throttle:1,0.025',
                        'intendedRedirection',
                    ],
                    'uses' => 'Clear\RegistrationController@register',
                ]);

                Route::post('register/verify-email', [
                    'as' => 'registration.verify-email',
                    'uses' => 'Clear\RegistrationController@verifyEmail',
                ]);

                Route::post('register/verify-mobile', [
                    'as' => 'registration.verify-mobile',
                    'uses' => 'Clear\RegistrationController@verifyMobile',
                ]);

                Route::get('verify/generate', [
                    'as' => 'auth.verify.generate',
                    'middleware' => [
                        'throttle:auth.verify.generate',
                        'isTempConsumer',
                        'tempConsumerWithGlobal',
                    ],
                    'uses' => 'Clear\VerificationController@generateLoginCode',
                ]);

                Route::match(['POST', 'GET'], 'verifyCode/{code?}/{token?}', [
                    'as' => 'auth.verify.confirm',
                    'middleware' => [
                        'throttle:auth.verify.confirm',
                        'preferredLanguage',
                        'isUserOrTemp',
                        'tempConsumerWithGlobal',
                    ],
                    'uses' => 'Clear\VerificationController@confirmLoginCode',
                ]);

                Route::get('verify/{token}', [
                    'as' => 'auth.verify.show',
                    'middleware' => [
                        'preferredLanguage',
                        'isTempConsumer',
                    ],
                    'uses' => 'Clear\VerificationController@showLoginCode',
                ]);

                Route::get('verify/confirm-channel/{confirmationToken}', [
                    'as' => 'verification.confirm-channel',
                    'middleware' => 'throttle:auth.verify.confirm',
                    'uses' => 'Clear\VerificationController@confirmChannel',
                ]);
            }
        );
        // Authentication
        Route::group(
            ['middleware' => 'authentication.exceptions', 'preferredLanguage'],
            function () {
                Route::post('login', [
                    'as' => 'login',
                    'middleware' => ['preferredLanguage'],
                    'uses' => 'AuthenticationController@login',
                ]);

                Route::get('login', [
                    'middleware' => [
                        'preferredLanguage',
                        'authentication.resolve-auth-token',
                        'verification',
                    ],
                    'uses' => 'AuthenticationController@loginToken',
                ]);

                Route::get('auth/social', [
                    'middleware' => [
                        'preferredLanguage',
                        'authentication.resolve-social-auth-token',
                        'verification',
                    ],
                    'uses' => 'AuthenticationController@loginSocialAuthToken',
                ]);

                Route::get(
                    'login/emulate/{token}',
                    'AuthenticationController@emulate'
                );

                Route::post('logout', [
                    'as' => 'logout',
                    'uses' => 'AuthenticationController@logout',
                ]);

                Route::get('auth/accounts', [
                    'middleware' => 'auth.user',
                    'uses' => 'AuthenticationController@getAccounts',
                ]);

                Route::get('auth/account/{globalAccount}', [
                    'middleware' => ['auth.user', 'jedi:deny'],
                    'uses' => 'AuthenticationController@provisionAccountSwitch',
                ]);

                Route::get('app/switch/{app}', [
                    'as' => 'app.switch',
                    'middleware' => 'auth.user',
                    'uses' => 'AuthenticationController@outgoingAppSwitch',
                ]);

                Route::get('app/switch', [
                    'middleware' => 'saml.auth.redirector',
                    'uses' => 'AuthenticationController@incomingAppSwitch',
                ]);

                Route::get('auth/confirm', [
                    'as' => 'auth.confirm',
                    'middleware' => Authenticate::class,
                    'uses' => 'AuthenticationConfirmController@login',
                ]);
                Route::post('auth/confirm', [
                    'as' => 'auth.confirm.check',
                    'middleware' => Authenticate::class,
                    'uses' => 'AuthenticationConfirmController@check',
                ]);

                Route::get('password/reset/{token}', [
                    'as' => 'password.reset',
                    'middleware' => 'preferredLanguage',
                    'uses' => 'AuthenticationController@passwordReset',
                ]);

                Route::post('password/reset', [
                    'as' => 'password.reset.update',
                    'uses' => 'AuthenticationController@updatePassword',
                ]);

                Route::post('login/request_link', [
                    'as' => 'login.request_link',
                    'uses' => 'AuthenticationController@requestLoginLink',
                ]);

                Route::get('login/link/{token}', [
                    'as' => 'login.link',
                    'uses' => 'AuthenticationController@loginLink',
                ]);
            }
        );

        // Awards
        Route::group([
            'middleware' => [
                'auth.role',
                'feature:awards.certificates',
            ],
        ], function () {
            Route::get('award', [
                'as' => 'award.index',
                'middleware' => 'remembrance',
                'uses' => 'AwardController@index',
            ]);

            Route::get('award/new', [
                'as' => 'award.new',
                'uses' => 'AwardController@new',
            ]);

            Route::post('award', [
                'as' => 'award.create',
                'uses' => 'AwardController@create',
            ]);

            Route::post('award/preview', [
                'as' => 'award.create.preview',
                'uses' => 'AwardController@createPreview',
            ]);

            Route::get('award/{award}', [
                'as' => 'award.edit',
                'uses' => 'AwardController@edit',
            ]);

            Route::put('award/{award}', [
                'as' => 'award.update',
                'uses' => 'AwardController@update',
            ]);

            Route::put('award/{award}/preview', [
                'as' => 'award.update.preview',
                'uses' => 'AwardController@updatePreview',
            ]);

            Route::post('award/copy', [
                'as' => 'award.copy',
                'uses' => 'AwardController@postCopy',
            ]);

            Route::delete('award', [
                'as' => 'award.delete',
                'uses' => 'AwardController@delete',
            ]);

            Route::put('award', [
                'as' => 'award.undelete',
                'uses' => 'AwardController@undelete',
            ]);

            Route::get('award/{award}/preview', [
                'as' => 'award.preview',
                'uses' => 'AwardController@certificatePreviewPDF',
            ]);

            Route::get('award/{award}/{entry}', [
                'as' => 'award.view',
                'uses' => 'AwardController@certificatePDF',
            ]);
        });

        // Broadcasts
        Route::group(
            ['middleware' => ['auth.role', 'feature:broadcasts']],
            function () {
                Route::get('broadcast', [
                    'as' => 'broadcast.index',
                    'middleware' => 'remembrance',
                    'uses' => 'BroadcastController@index',
                ]);

                Route::match(['get', 'post'], 'broadcast/new/{type}', [
                    'as' => 'broadcast.new',
                    'uses' => 'BroadcastController@getNew',
                ]);

                Route::post('broadcast', [
                    'as' => 'broadcast.create',
                    'uses' => 'BroadcastController@create',
                ]);

                Route::post('broadcast/review', [
                    'as' => 'broadcast.create.review',
                    'uses' => 'BroadcastController@createReview',
                ]);

                Route::get('broadcast/{broadcast}', [
                    'as' => 'broadcast.edit',
                    'uses' => 'BroadcastController@edit',
                ]);

                Route::put('broadcast/{broadcast}', [
                    'as' => 'broadcast.update',
                    'uses' => 'BroadcastController@update',
                ]);

                Route::delete('broadcast/{broadcast}', [
                    'as' => 'broadcast.delete',
                    'uses' => 'BroadcastController@delete',
                ]);

                Route::put('broadcast/{broadcast}/review', [
                    'as' => 'broadcast.update.review',
                    'uses' => 'BroadcastController@updateReview',
                ]);

                Route::put('broadcast/{broadcast}/{filter}', [
                    'as' => 'broadcast.remove-filter',
                    'uses' => 'BroadcastController@removeActiveFilter',
                ]);

                Route::get('broadcast/{broadcast}/review', [
                    'as' => 'broadcast.review',
                    'uses' => 'BroadcastController@review',
                ]);

                Route::post('broadcast/{broadcast}/send', [
                    'as' => 'broadcast.send',
                    'uses' => 'BroadcastController@send',
                ]);

                Route::post('broadcast/{broadcast}/schedule', [
                    'as' => 'broadcast.schedule',
                    'uses' => 'BroadcastController@schedule',
                ]);
            }
        );

        // Categories
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('category', [
                'as' => 'category.index',
                'middleware' => 'remembrance',
                'uses' => 'CategoryController@index',
            ]);

            Route::get('category/autocomplete', [
                'as' => 'category.autocomplete',
                'uses' => 'CategoryController@autocomplete',
            ]);

            Route::put('category/undelete', [
                'as' => 'category.undelete',
                'uses' => 'CategoryController@putUndelete',
            ]);

            Route::get('category/new', [
                'as' => 'category.new',
                'uses' => 'CategoryController@getNew',
            ]);

            Route::post('category', [
                'as' => 'category.create',
                'uses' => 'CategoryController@postCreate',
            ]);

            Route::get('category/{category}', [
                'as' => 'category.show',
                'uses' => 'CategoryController@getShow',
            ]);

            Route::put('category/{category}', [
                'as' => 'category.update',
                'uses' => 'CategoryController@putUpdate',
            ]);

            Route::post('category/copy', [
                'as' => 'category.copy',
                'uses' => 'CategoryController@postCopy',
            ]);

            Route::delete('category', [
                'as' => 'category.delete',
                'uses' => 'CategoryController@delete',
            ]);

            Route::delete('category/{category}/image/{id}', [
                'as' => 'category.delete.image',
                'uses' => 'CategoryController@deleteAttachment',
            ]);

            Route::get('category/by-form/{form}', [
                'as' => 'category.by-form',
                'uses' => 'CategoryController@getByForm',
            ]);
        });

        // Chapters
        Route::group([
            'middleware' => [
                'auth.role',
                'chapter.exceptions',
                RedirectIfNotMultiChapter::class,
            ],
        ], function () {
            Route::get('chapter', [
                'as' => 'chapter.index',
                'middleware' => 'remembrance',
                'uses' => 'ChapterController@index',
            ]);

            Route::put('chapter/undelete', [
                'as' => 'chapter.undelete',
                'uses' => 'ChapterController@putUndelete',
            ]);

            Route::get('chapter/add', [
                'as' => 'chapter.add',
                'uses' => 'ChapterController@add',
            ]);

            Route::post('chapter', [
                'as' => 'chapter.create',
                'uses' => 'ChapterController@create',
            ]);

            Route::get('chapter/{chapter}', [
                'as' => 'chapter.edit',
                'uses' => 'ChapterController@getEdit',
            ]);

            Route::put('chapter/{chapter}', [
                'as' => 'chapter.update',
                'middleware' => 'outdated',
                'uses' => 'ChapterController@putUpdate',
            ]);

            Route::post('chapter/copy', [
                'as' => 'chapter.copy',
                'uses' => 'ChapterController@postCopy',
            ]);

            Route::delete('chapter', [
                'as' => 'chapter.delete',
                'uses' => 'ChapterController@delete',
            ]);

            Route::delete('chapter/{chapter}/image/{id}', [
                'as' => 'chapter.delete.image',
                'uses' => 'ChapterController@deleteAttachment',
            ]);

            Route::post('chapter/set-default', [
                'as' => 'chapter.set-default',
                'uses' => 'ChapterController@setDefault',
            ]);
        });

        // Cloud Assets
        Route::group(['middleware' => GuestUser::class], function () {
            Route::get('cloud-asset-url', [
                'as' => 'cloud-asset-url',
                'uses' => 'CloudAssetController@cloudAssetUrl',
            ]);
        });

        // Columnator
        Route::group(['middleware' => 'auth.user'], function () {
            Route::get('columnator/{type}', [
                'as' => 'columnator.form',
                'uses' => 'ColumnatorController@form',
            ]);

            Route::post('columnator/{type}', [
                'as' => 'columnator.update',
                'uses' => 'ColumnatorController@update',
            ]);
        });

        // Comments
        Route::group(['middleware' => 'auth.user'], function () {
            Route::get('comment', [
                'as' => 'comment.get',
                'uses' => 'CommentController@get',
            ]);

            Route::post('comment', [
                'as' => 'comment.create',
                'uses' => 'CommentController@create',
            ]);

            Route::put('comment/{commentInstance}', [
                'as' => 'comment.update',
                'uses' => 'CommentController@update',
            ]);

            Route::delete('comment/{commentInstance}', [
                'as' => 'comment.delete',
                'uses' => 'CommentController@delete',
            ]);

            Route::delete('comment/file/{file}/{commentInstance?}', [
                'as' => 'comment.delete-file',
                'uses' => 'CommentController@deleteFile',
            ]);
        });

        // Content blocks
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('content/block', [
                'as' => 'content-block.index',
                'middleware' => 'remembrance',
                'uses' => 'ContentBlockController@index',
            ]);

            Route::get('content/block/new', [
                'as' => 'content-block.new',
                'uses' => 'ContentBlockController@getNew',
            ]);

            Route::post('content/block', [
                'as' => 'content-block.create',
                'uses' => 'ContentBlockController@create',
            ]);

            Route::get('content/block/{contentBlock}', [
                'as' => 'content-block.edit',
                'uses' => 'ContentBlockController@edit',
            ]);

            Route::put('content/block/undelete', [
                'as' => 'content-block.undelete',
                'uses' => 'ContentBlockController@undelete',
            ]);

            Route::put('content/block/{contentBlock}', [
                'as' => 'content-block.update',
                'middleware' => 'outdated',
                'uses' => 'ContentBlockController@update',
            ]);

            Route::delete('content/block', [
                'as' => 'content-block.delete',
                'uses' => 'ContentBlockController@delete',
            ]);

            Route::get('content/block/type/all', [
                'as' => 'content-block.type.all',
                'uses' => 'ContentBlockController@getContentBlockTypeAll',
            ]);
        });

        Route::group(['middleware' => ['contentBlockRoleRestriction']], function () {
            Route::get('about/{contentBlock}', [
                'as' => 'about.show',
                'uses' => 'ContentBlockController@viewAbout',
            ]);

            Route::get('page/{contentBlock}', [
                'as' => 'page.show',
                'middleware' => 'preferredLanguage',
                'uses' => 'ContentBlockController@viewPage',
            ]);
        });

        // Cookie notice
        Route::group([], function () {
            Route::post('cookies/accept', [
                'as' => 'cookies.accept',
                'uses' => 'CookieConsentController@accept',
            ]);
        });

        // Dashboard
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('dashboard', [
                'as' => 'dashboard.index',
                'uses' => 'DashboardController@index',
            ]);

            Route::get('report/{widget}', [
                'as' => 'report.view',
                'middleware' => 'remembrance',
                'uses' => 'DashboardController@viewReport',
            ]);
        });

        Route::controller(NewDashboardController::class)->middleware(DashboardEnabled::class)->group(function () {
            Route::get('dashboard-new/program-manager', [
                'as' => 'dashboard.programManager',
                'middleware' => ['roles:ProgramManager'],
                'uses' => 'programManager',
            ]);

            Route::get('dashboard-new/chapter-manager', [
                'as' => 'dashboard.chapterManager',
                'middleware' => ['roles:ChapterManager'],
                'uses' => 'chapterManager',
            ]);

            Route::get('dashboard-new/{provider}/{providerId}', [
                'as' => 'dashboard.custom',
                'middleware' => ['roles:ProgramManager', DashboardExists::class],
                'uses' => 'custom',
            ]);
        });

        // Discounts
        Route::group(
            ['middleware' => ['auth.role', 'feature:order_payments']],
            function () {
                Route::get('discount', [
                    'as' => 'discount',
                    'uses' => 'DiscountController@index',
                ]);

                Route::get('discount/new', [
                    'as' => 'discount.new',
                    'uses' => 'DiscountController@getNew',
                ]);

                Route::post('discount', [
                    'as' => 'discount.create',
                    'uses' => 'DiscountController@create',
                ]);

                Route::get('discount/{discount}', [
                    'as' => 'discount.edit',
                    'uses' => 'DiscountController@edit',
                ]);

                Route::put('discount/{discount}', [
                    'as' => 'discount.update',
                    'middleware' => 'outdated',
                    'uses' => 'DiscountController@update',
                ]);

                Route::delete('discount/{discount}', [
                    'as' => 'discount.delete',
                    'uses' => 'DiscountController@delete',
                ]);
            }
        );

        // Downloads
        Route::group(['middleware' => ['isOwnerOrProgramManager']], function () {
            Route::get('download', [
                'as' => 'download.list',
                'middleware' => ['remembrance'],
                'uses' => 'DownloadController@index',
            ]);
        });

        // E-commerce
        Route::group([
            'middleware' => [
                //'auth.user',
                //PaymentConfigurationCheck::class,
            ],
        ], function () {
            Route::get('cart', [
                'as' => 'cart.view',
                'uses' => 'CartController@view',
            ]);

            Route::post('cart/tag/{price}/{entry}', [
                'as' => 'cart.tag.pay',
                'uses' => 'CartController@payTagFee',
            ]);

            Route::get('cart/items-count', [
                'as' => 'cart.items.count',
                'middleware' => ExpectJson::class,
                'uses' => 'CartController@itemsCount',
            ]);

            Route::group(['middleware' => EmptyCart::class], function () {
                Route::post('cart', [
                    'as' => 'cart.update',
                    'middleware' => DiscountValidity::class,
                    'uses' => 'CartController@update',
                ]);

                Route::post('cart/pay', [
                    'as' => 'cart.pay',
                    'middleware' => DiscountValidity::class,
                    'uses' => 'CartController@pay',
                ]);

                Route::get('cart/checkout', [
                    'as' => 'cart.checkout',
                    'uses' => 'CartController@checkout',
                    'middleware' => CartIsReadyForOnsitePayment::class,
                ]);

                Route::post('cart/checkout/challenge', [
                    'as' => 'cart.challenge',
                    'uses' => 'CartController@challenge',
                    'middleware' => [
                        CartIsReadyForOnsitePayment::class,
                        CheckoutDiscountValidity::class,
                    ],
                ]);

                Route::post('cart/checkout', [
                    'as' => 'cart.process',
                    'uses' => 'CartController@process',
                    'middleware' => [
                        CartIsReadyForOnsitePayment::class,
                        CheckoutDiscountValidity::class,
                    ],
                ]);

                Route::get('cart/{key}', [
                    'as' => 'cart.remove',
                    'uses' => 'CartController@remove',
                ]);
            });
        });

        // Entry form - Entrant's and Manager's views
        Route::group(
            ['namespace' => 'EntryForm', 'middleware' => ['auth.user', 'auth.role']],
            function () {
                $controllers = [
                    'entrant' => 'EntrantEntryFormController',
                    'manager' => 'ManagerEntryFormController',
                ];

                foreach ($controllers as $type => $class) {
                    Route::get(
                        "entry-form/$type/categories/{seasonId}/{chapterId}",
                        [
                            'as' => "entry.$type.chapter.categories",
                            'uses' => "$class@getCategories",
                        ]
                    );

                    Route::get("entry-form/$type/start", [
                        'as' => "entry-form.$type.start",
                        'middleware' => $type === 'manager'
                            ? []
                            : [
                                'round:entry',
                                FormRoleAccess::class,
                                'entryLimitReached',
                                'user.confirmed',
                            ],
                        'uses' => "$class@getStart",
                    ]);

                    Route::get("entry-form/$type/fields/{tab}/{entry?}", [
                        'as' => "entry-form.$type.fields",
                        'uses' => "$class@getFields",
                    ]);

                    Route::get("entry-form/$type/tabs/{entry?}", [
                        'as' => "entry-form.$type.tabs",
                        'uses' => "$class@getTabs",
                    ]);

                    Route::get("entry-form/$type/contributors/{entry}", [
                        'as' => "entry-form.$type.contributors",
                        'middleware' => $type === 'entrant' ? ['collaborator']
                            : [],
                        'uses' => "$class@getContributors",
                    ]);

                    Route::get("entry-form/$type/attachments/{entry}", [
                        'as' => "entry-form.$type.attachments",
                        'middleware' => $type === 'entrant' ? ['collaborator']
                            : [],
                        'uses' => "$class@getAttachments",
                    ]);

                    Route::get("entry-form/$type/referees/{entry}", [
                        'as' => "entry-form.$type.referees",
                        'middleware' => $type === 'entrant' ? ['collaborator']
                            : [],
                        'uses' => "$class@referees",
                    ]);

                    Route::post("entry-form/$type/start", [
                        'as' => "entry-form.$type.startCreate",
                        'middleware' => $type === 'manager'
                            ? []
                            : [
                                'round:entry',
                                FormRoleAccess::class,
                                'entryLimitReached',
                                'user.confirmed',
                            ],
                        'uses' => "$class@startEntry",
                    ]);

                    Route::get("entry-form/$type/{entry}/edit", [
                        'as' => "entry-form.$type.edit",
                        'middleware' => $type === 'manager' ? ['manager']
                            : [
                                'auth.user',
                                'collaborator',
                            ],
                        'uses' => "$class@getEdit",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/autosave", [
                        'as' => "entry-form.$type.autosave",
                        'middleware' => $type === 'manager'
                            ? [
                                'useFormSessionUuid',
                                'outdated',
                            ]
                            : [
                                'useFormSessionUuid',
                                'round:entry,allowResubmission',
                                'collaborator',
                                'outdated',
                            ],
                        'uses' => "$class@autosave",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/update-field/{field}", [
                        'as' => "entry-form.$type.update-field",
                        'middleware' => $entryUpdateMiddleware = ($type === 'manager'
                            ? [
                                'useFormSessionUuid',
                            ]
                            : [
                                'useFormSessionUuid',
                                'round:entry,allowResubmission',
                                'collaborator',
                            ]),
                        'uses' => "$class@updateField",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/update-attachment/{attachment}/{field}", [
                        'as' => "entry-form.$type.update-attachment",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@updateAttachment",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/create-contributor/{tab}", [
                        'as' => "entry-form.$type.create-contributor",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@createContributor",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/update-contributor/{contributor}/{field}", [
                        'as' => "entry-form.$type.update-contributor",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@updateContributor",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/remove-contributor/{contributor}", [
                        'as' => "entry-form.$type.remove-contributor",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@removeContributor",
                    ]);

                    // Referees
                    Route::put("entry-form/$type/entry/{entry}/create-referee/{tab}", [
                        'as' => "entry-form.$type.create-referee",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@createReferee",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/update-referee/{referee}", [
                        'as' => "entry-form.$type.update-referee",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@updateReferee",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/update-referee/{referee}/{field}", [
                        'as' => "entry-form.$type.update-referee-field",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@updateRefereeField",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/remove-referee/{referee}", [
                        'as' => "entry-form.$type.remove-referee",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@removeReferee",
                    ]);

                    // Links
                    Route::put("entry-form/$type/entry/{entry}/create-link/{tab}", [
                        'as' => "entry-form.$type.create-link",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@createLink",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/update-link/{link}/", [
                        'as' => "entry-form.$type.update-link",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@updateLink",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/delete-link/{link}", [
                        'as' => "entry-form.$type.delete-link",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@deleteLink",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/update-title", [
                        'as' => "entry-form.$type.update-title",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@updateTitle",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/update-category", [
                        'as' => "entry-form.$type.update-category",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@updateCategory",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/update-chapter", [
                        'as' => "entry-form.$type.update-chapter",
                        'middleware' => $entryUpdateMiddleware,
                        'uses' => "$class@updateChapter",
                    ]);

                    Route::put("entry-form/$type/entry/submit", [
                        'as' => "entry-form.$type.startAndSubmit",
                        'middleware' => $type === 'manager' ? []
                            : ['round:entry'],
                        'uses' => "$class@startAndSubmit",
                    ]);

                    Route::put("entry-form/$type/entry/{entry}/submit", [
                        'as' => "entry-form.$type.updateAndSubmit",
                        'middleware' => $type === 'manager'
                            ? [
                                'useFormSessionUuid',
                                ConditionalOutdated::class,
                            ]
                            : [
                                'useFormSessionUuid',
                                'round:entry,allowResubmission',
                                'collaborator',
                                ConditionalOutdated::class,
                            ],
                        'uses' => "$class@updateAndSubmit",
                    ]);

                    Route::post("entry-form/$type/entry/{entry}/eligibility", [
                        'as' => "entry-form.$type.updateAndSubmitEligibility",
                        'middleware' => $type === 'manager'
                            ? [
                                'useFormSessionUuid',
                                ConditionalOutdated::class,
                            ]
                            : [
                                'useFormSessionUuid',
                                'round:entry,allowResubmission',
                                'collaborator',
                                ConditionalOutdated::class,
                            ],
                        'uses' => "$class@updateAndSubmitEligibility",
                    ]);

                    Route::get(
                        "entry-form/$type/fields-for-conditional/{entry?}",
                        [
                            'as' => "entry-form.$type.fields-for-conditional",
                            'middleware' => $type === 'entrant'
                                ? ['collaborator'] : [],
                            'uses' => "$class@getFieldsForConditional",
                        ]
                    );

                    Route::post(
                        "entry-form/$type/referee/review-stage/{entry}",
                        [
                            'as' => "entry-form.$type.referee.initiate-review-stage",
                            'middleware' => $type === 'entrant'
                                ? ['owner'] : [],
                            'uses' => "$class@initiateRefereeReviewStage",
                        ]
                    );

                    Route::post(
                        "entry-form/$type/referee/resend/review-task/{reviewTask}",
                        [
                            'as' => "entry-form.$type.referee.resend.review-task",
                            'uses' => "$class@resendRefereeReviewTask",
                        ]
                    );
                }
                Route::get('entry/manager/{category}/blank-pdf/{chapter?}', [
                    'as' => 'entry.manager.blank-pdf',
                    'middleware' => ['feature:pdfs'],
                    'uses' => 'ManagerEntryFormController@blankPdf',
                ]);
            }
        );

        Route::group(['namespace' => 'EntryForm', 'middleware' => 'auth.role'], function () {
            Route::get('entry/entrant/{category}/blank-pdf/{chapter?}', [
                'as' => 'entry.entrant.blank-pdf',
                'middleware' => ['feature:pdfs'],
                'uses' => 'EntrantEntryFormController@blankPdf',
            ]);
        });

        // Entry form - Manager's view
        Route::group(
            ['namespace' => 'EntryForm', 'middleware' => 'auth.role'],
            function () {
                Route::post('entry-form/manager/update-owner/{entry}/{user}', [
                    'as' => 'entry-form.manager.update-owner',
                    'uses' => 'ManagerEntryFormController@updateEntryOwner',
                ]);
            }
        );

        // Entry form - Configuration - Categories
        Route::group([
            'namespace' => 'EntryForm\Configuration',
            'middleware' => 'auth.role',
        ], function () {
            Route::put('entry-form/manager/categories/{category}', [
                'as' => 'entry-form.manager.categories.update',
                'uses' => 'CategoryController@update',
            ]);

            Route::post('entry-form/manager/categories', [
                'as' => 'entry-form.manager.categories.add',
                'middleware' => 'formRestricted:entry',
                'uses' => 'CategoryController@add',
            ]);

            Route::delete('entry-form/manager/categories/{category}', [
                'as' => 'entry-form.manager.categories.delete',
                'uses' => 'CategoryController@delete',
            ]);

            Route::get('entry-form/manager/categories/{category}', [
                'as' => 'entry-form.manager.categories.reload',
                'uses' => 'CategoryController@reload',
            ]);
        });

        // Entry form - Configuration - Chapters
        Route::group([
            'namespace' => 'EntryForm\Configuration',
            'middleware' => 'auth.role',
        ], function () {
            Route::put('entry-form/manager/chapters/{chapter}', [
                'as' => 'entry-form.manager.chapters.update',
                'uses' => 'ChapterController@update',
            ]);

            Route::post('entry-form/manager/chapters', [
                'as' => 'entry-form.manager.chapters.add',
                'middleware' => 'formRestricted:entry',
                'uses' => 'ChapterController@add',
            ]);

            Route::delete('entry-form/manager/chapters/{chapter}', [
                'as' => 'entry-form.manager.chapters.delete',
                'uses' => 'ChapterController@delete',
            ]);

            Route::get('entry-form/manager/chapters/{chapter}', [
                'as' => 'entry-form.manager.chapters.reload',
                'uses' => 'ChapterController@reload',
            ]);
        });

        // Entry form - Configuration - Form
        Route::group([
            'namespace' => 'EntryForm\Configuration',
            'middleware' => 'auth.role',
        ], function () {
            Route::put('entry-form/manager/form/{form}', [
                'as' => 'entry-form.manager.form.update',
                'uses' => 'FormController@update',
            ]);

            Route::delete('entry-form/manager/form/{form}/image/{id}', [
                'as' => 'entry-form.manager.form.delete.image',
                'uses' => 'FormController@deleteCoverImage',
            ]);
        });

        // Grant reports
        Route::group(['middleware' => ['auth.role', 'feature:grant_reports']], function () {
            Route::post('grant-report/create', [
                'as' => 'grant-report.create',
                'uses' => 'ManagerGrantReportController@create',
            ]);

            Route::delete('grant-report/{grantReport?}', [
                'as' => 'grant-report.delete',
                'uses' => 'ManagerGrantReportController@delete',
            ]);

            Route::put('grant-report/undelete', [
                'as' => 'grant-report.undelete',
                'uses' => 'ManagerGrantReportController@undelete',
            ]);

            Route::get('grant-report/entrant/{grantReport}/preview', [
                'as' => 'grant-report.entrant.preview',
                'uses' => 'EntrantGrantReportController@preview',
            ]);

            Route::get('grant-report/manager/{grantReport}/preview', [
                'as' => 'grant-report.manager.preview',
                'uses' => 'ManagerGrantReportController@preview',
            ]);

            Route::put('grant-report/{grantReport}/update-due-date', [
                'as' => 'grant-report.update.due_date',
                'uses' => 'ManagerGrantReportController@updateDueDate',
            ]);

            Route::put('grant-report/update-due-date', [
                'as' => 'grant-report.manager.bulk_update_due_date',
                'uses' => 'ManagerGrantReportController@bulkUpdateDueDate',
            ]);

            Route::put('grant-report/entrant/{grantReport}/attachment/order', [
                'as' => 'grant-report.entrant.order.attachments',
                'uses' => 'EntrantGrantReportController@orderAttachments',
            ]);

            Route::put('grant-report/manager/{grantReport}/attachment/order', [
                'as' => 'grant-report.manager.order.attachments',
                'uses' => 'ManagerGrantReportController@orderAttachments',
            ]);

            Route::delete('grant-report/manager/{grantReport}/attachment/{id}', [
                'as' => 'grant-report.manager.delete.attachment',
                'uses' => 'ManagerGrantReportController@deleteAttachment',
            ]);

            Route::get('grant-report/entrant/complete/{grantReport?}', [
                'as' => 'grant-report.entrant.complete',
                'uses' => 'ManagerGrantReportController@complete',
            ]);

            Route::get('grant-report/manager/complete/{grantReport?}', [
                'as' => 'grant-report.manager.complete',
                'uses' => 'EntrantGrantReportController@complete',
            ]);

            Route::delete('grant-report/manager/{grantReport}/file/{file}', [
                'as' => 'grant-report.manager.delete.file',
                'uses' => 'ManagerGrantReportController@deleteFile',
            ]);

            Route::delete('grant-report/entrant/{grantReport}/file/{file}', [
                'as' => 'grant-report.entrant.delete.file',
                'uses' => 'EntrantGrantReportController@deleteFile',
            ]);

            Route::delete('grant-report/manager/{grantReport}/attachment/{id}', [
                'as' => 'grant-report.manager.delete.attachment',
                'uses' => 'ManagerGrantReportController@deleteAttachment',
            ]);

            Route::delete('grant-report/entrant/{grantReport}/attachment/{id}', [
                'as' => 'grant-report.entrant.delete.attachment',
                'uses' => 'EntrantGrantReportController@deleteAttachment',
            ]);

            Route::get('grant-report/entrant', [
                'as' => 'grant-report.entrant.index',
                'middleware' => 'remembrance',
                'uses' => 'EntrantGrantReportController@index',
            ]);

            Route::get('grant-report/manager', [
                'as' => 'grant-report.manager.index',
                'middleware' => 'remembrance',
                'uses' => 'ManagerGrantReportController@index',
            ]);

            Route::post('grant-report/entrant/download', [
                'as' => 'grant-report.entrant.download',
                'uses' => 'EntrantGrantReportController@download',
            ]);

            Route::post('grant-report/manager/download', [
                'as' => 'grant-report.manager.download',
                'uses' => 'ManagerGrantReportController@download',
            ]);

            Route::post('grant-report/manager/tag', [
                'as' => 'grant-report.manager.tag',
                'uses' => 'ManagerGrantReportController@tag',
            ]);

            Route::delete('grant-report/manager/untag', [
                'as' => 'grant-report.manager.untag',
                'uses' => 'ManagerGrantReportController@untag',
            ]);

            Route::get('grant-report/entrant/{form}/{submittable}/tokens', [
                'as' => 'grant-report.entrant.get.tokens',
                'uses' => 'EntrantGrantReportController@firebaseTokens',
            ]);
        });

        // Grant report form
        Route::group(
            ['namespace' => 'GrantReportForm', 'middleware' => 'auth.role'],
            function () {
                $controllers = [
                    'entrant' => 'EntrantGrantReportFormController',
                    'manager' => 'ManagerGrantReportFormController',
                ];

                Route::get('grant-report/manager/start', [
                    'as' => 'grant-report.form.edit',
                    'uses' => 'ManagerGrantReportFormController@getFormEdit',
                ]);

                foreach ($controllers as $type => $class) {
                    Route::get(
                        "grant-report/$type/categories/{seasonId}/{chapterId}",
                        [
                            'as' => "grant-report.$type.chapter.categories",
                            'middleware' => $type === 'entrant' ? ['collaborator'] : [],
                            'uses' => "$class@getCategories",
                        ]
                    );

                    Route::get("grant-report/$type/fields/{tab}/{grantReport?}", [
                        'as' => "grant-report.$type.fields",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : [],
                        'uses' => "$class@getFields",
                    ]);

                    Route::get("grant-report/$type/tabs/{grantReport?}", [
                        'as' => "grant-report.$type.tabs",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : [],
                        'uses' => "$class@getTabs",
                    ]);

                    Route::get("grant-report/$type/contributors/{grantReport}", [
                        'as' => "grant-report.$type.contributors",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : [],
                        'uses' => "$class@getContributors",
                    ]);

                    Route::get("grant-report/$type/attachments/{grantReport}", [
                        'as' => "grant-report.$type.attachments",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : [],
                        'uses' => "$class@getAttachments",
                    ]);

                    Route::get("grant-report/$type/{grantReport}/edit", [
                        'as' => "grant-report.$type.edit",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@getEdit",
                    ]);

                    Route::put("grant-report/$type/entry/{grantReport}/autosave", [
                        'as' => "grant-report.$type.autosave",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['outdated', 'manager'],
                        'uses' => "$class@autosave",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/update-field/{field}", [
                        'as' => "grant-report.$type.update-field",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@updateField",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/update-attachment/{field}", [
                        'as' => "grant-report.$type.update-attachment",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@updateAttachment",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/create-contributor/{tab}", [
                        'as' => "grant-report.$type.create-contributor",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@createContributor",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/update-contributor/{contributor}/{field}", [
                        'as' => "grant-report.$type.update-contributor",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@updateContributor",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/remove-contributor/{contributor}", [
                        'as' => "grant-report.$type.remove-contributor",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@removeContributor",
                    ]);

                    // Referee
                    Route::put("grant-report/$type/grant-report/{grantReport}/create-referee/{tab}", [
                        'as' => "grant-report.$type.create-referee",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@createReferee",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/update-referee/{referee}", [
                        'as' => "grant-report.$type.update-referee",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@updateReferee",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/update-referee/{referee}/{field}", [
                        'as' => "grant-report.$type.update-referee-field",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@updateRefereeField",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/remove-referee/{referee}", [
                        'as' => "grant-report.$type.remove-referee",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@removeReferee",
                    ]);

                    // Links
                    Route::put("grant-report/$type/grant-report/{grantReport}/create-link/{tab}", [
                        'as' => "grant-report.$type.create-link",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@createLink",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/update-link/{link}", [
                        'as' => "grant-report.$type.update-link",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@updateLink",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/delete-link/{link}", [
                        'as' => "grant-report.$type.delete-link",
                        'middleware' => $type === 'entrant' ? ['collaborator'] : ['manager'],
                        'uses' => "$class@deleteLink",
                    ]);

                    Route::put("grant-report/$type/grant-report/{grantReport}/submit", [
                        'as' => "grant-report.$type.updateAndSubmit",
                        'uses' => "$class@updateAndSubmit",
                    ]);

                    Route::get(
                        "grant-report/$type/fields-for-conditional/{grantReport?}",
                        [
                            'as' => "grant-report.$type.fields-for-conditional",
                            'middleware' => $type === 'entrant' ? ['collaborator'] : [],
                            'uses' => "$class@getFieldsForConditional",
                        ]
                    );
                }
            }
        );

        // Entry form - Entrants's view
        Route::group([
            'namespace' => 'EntryForm',
            'middleware' => 'owner',
        ], function () {
            Route::post('entry-form/entrant/{entry}/pay', [
                'as' => 'entry-form.entrant.pay',
                'middleware' => [
                    'round:entry',
                    'outdated',
                    PaymentConfigurationCheck::class,
                ],
                'uses' => 'EntrantEntryFormController@pay',
            ]);
        });

        // Collaborators
        Route::group(['middleware' => ['auth.role', 'feature:collaboration']],
            function () {
                Route::get('collaborators/{form}/{submittableSlug}', [
                    'as' => 'collaborators.index',
                    'uses' => 'CollaboratorController@collaborators',
                ]);

                Route::post('collaborators/{form}/{submittableSlug}/invite', [
                    'as' => 'collaborators.invite',
                    'uses' => 'CollaboratorController@invite',
                ]);

                Route::post('collaborators/{collaborator}/reinvite', [
                    'as' => 'collaborators.reinvite',
                    'uses' => 'CollaboratorController@reinvite',
                ]);

                Route::post('collaborators/{form}/{submittableSlug}/transfer-ownership/{user}', [
                    'as' => 'collaborators.owner',
                    'uses' => 'CollaboratorController@transferOwnership',
                ]);

                Route::put('collaborators/{collaborator}', [
                    'as' => 'collaborators.privilege.update',
                    'uses' => 'CollaboratorController@updatePrivilege',
                ]);

                Route::delete('collaborators/{collaborator}', [
                    'as' => 'collaborators.delete',
                    'uses' => 'CollaboratorController@delete',
                ]);
            }
        );

        // Entries - Entrant's view
        Route::group(
            ['namespace' => 'Entry', 'middleware' => 'auth.role'],
            function () {
                Route::get('entry/entrant', [
                    'as' => 'entry.entrant.index',
                    'middleware' => 'remembrance',
                    'uses' => 'EntrantEntryController@index',
                ]);

                Route::get('entry/entrant/start', [
                    'as' => 'entry.entrant.start',
                    'middleware' => [
                        'round:entry',
                        FormRoleAccess::class,
                        'entryLimitReached',
                        'user.confirmed',
                    ],
                    'uses' => 'EntrantEntryController@getStart',
                ]);

                Route::post('entry/entrant/copy', [
                    'as' => 'entry.entrant.copy',
                    'middleware' => ['round:entry', 'entryLimitReached'],
                    'uses' => 'EntrantEntryController@copy',
                ]);

                Route::post('entry/entrant/download', [
                    'as' => 'entry.entrant.download',
                    'uses' => 'EntrantEntryController@download',
                ]);

                Route::delete('entry/entrant', [
                    'as' => 'entry.entrant.delete',
                    'middleware' => 'round:entry',
                    'uses' => 'EntrantEntryController@delete',
                ]);

                Route::put('entry/entrant/undelete', [
                    'as' => 'entry.entrant.undelete',
                    'middleware' => ['round:entry', 'entryLimitReached'],
                    'uses' => 'EntrantEntryController@undelete',
                ]);

                Route::get('entry/entrant/{entry}/preview', [
                    'as' => 'entry.entrant.preview',
                    'middleware' => ['collaborator'],
                    'uses' => 'EntrantEntryController@preview',
                ]);

                Route::get('entry/entrant/{entry}/pdf', [
                    'as' => 'entry.entrant.pdf',
                    'middleware' => ['collaborator', 'feature:pdfs'],
                    'uses' => 'EntrantEntryController@pdf',
                ]);

                Route::get('entry/entrant/complete/{entry?}', [
                    'as' => 'entry.entrant.complete',
                    'uses' => 'EntrantEntryController@complete',
                ]);

                Route::get('entry/entrant/eligibility/{entry}/{contentBlock}', [
                    'as' => 'entry.entrant.eligibility',
                    'uses' => 'EntrantEntryController@eligibility',
                ]);

                Route::get('entry/entrant/{entry}', [
                    'as' => 'entry.entrant.edit',
                    'middleware' => 'collaborator',
                    'uses' => 'EntrantEntryController@edit',
                ]);

                Route::delete('entry/entrant/{entry}/attachment/{id}', [
                    'as' => 'entry.entrant.delete.attachment',
                    'middleware' => [
                        'round:entry,allowResubmission',
                        'collaborator',
                    ],
                    'uses' => 'EntrantEntryController@deleteAttachment',
                ]);

                Route::delete('entry/entrant/{entry}/file/{file}', [
                    'as' => 'entry.entrant.delete.file',
                    'middleware' => [
                        'round:entry,allowResubmission',
                        'collaborator',
                    ],
                    'uses' => 'EntrantEntryController@deleteFile',
                ]);

                Route::put('entry/entrant/{entry}/attachment/order', [
                    'as' => 'entry.entrant.order.attachments',
                    'middleware' => [
                        'round:entry,allowResubmission',
                        'collaborator',
                    ],
                    'uses' => 'EntrantEntryController@orderAttachments',
                ]);

                Route::get('entry/entrant/{entry}/packing-slip', [
                    'as' => 'entry.entrant.packing_slip',
                    'middleware' => ['owner', 'feature:pdfs', 'feature:packing_slips'],
                    'uses' => 'EntrantEntryController@packingSlip',
                ]);

                Route::get('entry/entrant/{entry}/contract/{contract}', [
                    'as' => 'entry.entrant.contract',
                    'middleware' => ['owner', 'feature:contracts'],
                    'uses' => 'EntrantEntryController@contract',
                ]);

                Route::post('entry/entrant/{entry}/contract/{contract}', [
                    'as' => 'entry.entrant.sign-contract',
                    'middleware' => ['owner', 'feature:contracts'],
                    'uses' => 'EntrantEntryController@signContract',
                ]);

                Route::get('entry/entrant/{entry}/contract/{contract}/pdf', [
                    'as' => 'entry.entrant.contract-pdf',
                    'middleware' => ['owner', 'feature:contracts'],
                    'uses' => 'EntrantEntryController@contractPdf',
                ]);

                Route::delete('entry/entrant/signature/{contract}/attachment/{id}', [
                    'as' => 'entry.entrant.delete.signature',
                    'uses' => 'EntrantEntryController@deleteSignatureAttachment',
                ]);

                Route::get('entry/entrant/{form}/{submittable}/tokens', [
                    'as' => 'entry.entrant.get.tokens',
                    'uses' => 'EntrantEntryController@firebaseTokens',
                ]);
            }
        );

        // Entries - manager view
        Route::group(
            ['namespace' => 'Entry', 'middleware' => 'auth.role'],
            function () {
                Route::get('entry/manager', [
                    'as' => 'entry.manager.index',
                    'middleware' => 'remembrance',
                    'uses' => 'ManagerEntryController@index',
                ]);

                Route::get('entry/autocomplete', [
                    'as' => 'entry.autocomplete',
                    'uses' => 'ManagerEntryController@autocomplete',
                ]);

                Route::get('entry/manager/start', [
                    'as' => 'entry.manager.start',
                    'uses' => 'ManagerEntryController@getStart',
                ]);

                Route::get('entry/manager/complete/{entry?}', [
                    'as' => 'entry.manager.complete',
                    'uses' => 'ManagerEntryController@complete',
                ]);

                Route::get('entry/manager/eligibility/{entry}/{contentBlock}', [
                    'as' => 'entry.manager.eligibility',
                    'uses' => 'ManagerEntryController@eligibility',
                ]);

                Route::post('entry/manager/copy', [
                    'as' => 'entry.manager.copy',
                    'uses' => 'ManagerEntryController@copy',
                ]);

                Route::post('entry/manager/download', [
                    'as' => 'entry.manager.download',
                    'uses' => 'ManagerEntryController@download',
                ]);

                Route::post('entry/manager/tag', [
                    'as' => 'entry.manager.tag',
                    'uses' => 'ManagerEntryController@tag',
                ]);

                Route::delete('entry/manager/untag', [
                    'as' => 'entry.manager.untag',
                    'uses' => 'ManagerEntryController@untag',
                ]);

                Route::post('entry/manager/tag/{entry}', [
                    'as' => 'entry.manager.tag.entry',
                    'uses' => 'ManagerEntryController@tagEntry',
                ]);

                Route::post('entry/manager/review-stage', [
                    'as' => 'entry.manager.initiate-review-stage',
                    'uses' => 'ManagerEntryController@initiateReviewStage',
                ]);

                Route::post('entry/manager/moderate', [
                    'as' => 'entry.manager.moderate',
                    'uses' => 'ManagerEntryController@moderate',
                ]);

                Route::post('entry/manager/moderate/{entry}', [
                    'as' => 'entry.manager.moderate.entry',
                    'uses' => 'ManagerEntryController@moderateEntry',
                ]);

                Route::delete('entry/manager', [
                    'as' => 'entry.manager.delete',
                    'uses' => 'ManagerEntryController@delete',
                ]);

                Route::put('entry/manager/undelete', [
                    'as' => 'entry.manager.undelete',
                    'uses' => 'ManagerEntryController@undelete',
                ]);

                Route::get('entry/manager/{entry}/destroy', [
                    'as' => 'entry.manager.destroy',
                    'uses' => 'ManagerEntryController@destroy',
                ]);

                Route::delete('entry/manager/{entry}/destroy', [
                    'as' => 'entry.manager.destroy.confirm',
                    'uses' => 'ManagerEntryController@confirmDestroy',
                ]);

                Route::put('entry/manager/archive', [
                    'as' => 'entry.manager.archive',
                    'uses' => 'ManagerEntryController@archive',
                ]);

                Route::put('entry/manager/unarchive', [
                    'as' => 'entry.manager.unarchive',
                    'uses' => 'ManagerEntryController@unarchive',
                ]);

                Route::post('entry/manager/add-contract', [
                    'as' => 'entry.manager.add-contract-to-selected-entries',
                    'middleware' => ['feature:contracts'],
                    'uses' => 'ManagerEntryController@addContractToSelectedEntries',
                ]);

                Route::post('entry/manager/require-resubmission', [
                    'as' => 'entry.manager.require-resubmission',
                    'middleware' => ['feature:resubmission'],
                    'uses' => 'ManagerEntryController@requireBulkResubmission',
                ]);

                Route::get('entry/manager/{entry}', [
                    'as' => 'entry.manager.view',
                    'middleware' => 'manager',
                    'uses' => 'ManagerEntryController@view',
                ]);

                Route::post('entry/manager/{entry}', [
                    'as' => 'entry.manager.postView',
                    'middleware' => 'manager',
                    'uses' => 'ManagerEntryController@postView',
                ]);

                Route::get('entry/manager/{entry}/preview', [
                    'as' => 'entry.manager.preview',
                    'middleware' => 'manager',
                    'uses' => 'ManagerEntryController@preview',
                ]);

                Route::get('entry/manager/{entry}/edit', [
                    'as' => 'entry.manager.edit',
                    'middleware' => 'manager',
                    'uses' => 'ManagerEntryController@edit',
                ]);

                Route::get('entry/manager/{entry}/clear-health', [
                    'as' => 'entry.manager.clear-health',
                    'middleware' => 'manager',
                    'uses' => 'ManagerEntryController@clearEntryHealthStatus',
                ]);

                Route::post('entry/manager/{entry}/recuse', [
                    'as' => 'entry.manager.recuse',
                    'middleware' => 'manager',
                    'uses' => 'ManagerEntryController@recuse',
                ]);

                Route::delete('entry/manager/{entry}/recuse', [
                    'as' => 'entry.manager.unrecuse',
                    'middleware' => 'manager',
                    'uses' => 'ManagerEntryController@unrecuse',
                ]);

                Route::delete('entry/manager/{entry}/attachment/{id}', [
                    'as' => 'entry.manager.delete.attachment',
                    'uses' => 'ManagerEntryController@deleteAttachment',
                ]);

                Route::put('entry/manager/{entry}/attachment/order', [
                    'as' => 'entry.manager.order.attachments',
                    'uses' => 'ManagerEntryController@orderAttachments',
                ]);

                Route::put('entry/manager/{entry}/revert_to_in_progress', [
                    'as' => 'entry.manager.revert_to_in_progress',
                    'uses' => 'ManagerEntryController@revertToInProgress',
                ]);

                Route::put('entry/manager/{entry}/require_resubmission', [
                    'as' => 'entry.manager.require_resubmission',
                    'middleware' => ['feature:resubmission'],
                    'uses' => 'ManagerEntryController@requireResubmission',
                ]);

                Route::get('entry/manager/{entry}/entrant-pdf', [
                    'as' => 'entry.manager.entrant-pdf',
                    'middleware' => ['feature:pdfs'],
                    'uses' => 'ManagerEntryController@entrantPDF',
                ]);

                Route::get('entry/manager/{entry}/manager-pdf', [
                    'as' => 'entry.manager.manager-pdf',
                    'middleware' => ['feature:pdfs', 'roles:Manager'],
                    'uses' => 'ManagerEntryController@managerPDF',
                ]);

                Route::get('entry/manager/{entry}/packing-slip', [
                    'as' => 'entry.manager.packing_slip',
                    'middleware' => ['feature:pdfs', 'feature:packing_slips'],
                    'uses' => 'ManagerEntryController@packingSlip',
                ]);

                Route::delete('entry/manager/{entry}/file/{file}', [
                    'as' => 'entry.manager.delete.file',
                    'uses' => 'ManagerEntryController@deleteFile',
                ]);

                Route::post(
                    'entry/manager/{entry}/add-contract/{contentBlock}',
                    [
                        'as' => 'entry.manager.add-contract',
                        'middleware' => ['feature:contracts'],
                        'uses' => 'ManagerEntryController@addContract',
                    ]
                );

                Route::delete(
                    'entry/manager/{entry}/delete-contract/{contract}',
                    [
                        'as' => 'entry.manager.delete-contract',
                        'middleware' => ['feature:contracts'],
                        'uses' => 'ManagerEntryController@deleteContract',
                    ]
                );

                Route::get('entry/manager/{entry}/contract/{contract}/pdf', [
                    'as' => 'entry.manager.contract-pdf',
                    'middleware' => ['feature:contracts'],
                    'uses' => 'ManagerEntryController@contractPdf',
                ]);

                Route::post('entry/manager/{entry}/deadline', [
                    'as' => 'entry.manager.deadline',
                    'middleware' => 'manager',
                    'uses' => 'ManagerEntryController@setDeadline',
                ]);
            }
        );

        // Manage duplicates
        Route::group([
            'prefix' => 'entry/duplicates',
            'middleware' => [
                'auth.role',
                'roles:Manager',
                'feature:manage_duplicates',
            ],
        ], function () {
            Route::get('manage', [
                'as' => 'entry.duplicates.manage',
                'middleware' => 'remembrance',
                'uses' => 'ManageDuplicatesController@index',
            ]);

            Route::get('scan', [
                'as' => 'entry.duplicates.scan',
                'uses' => 'ManageDuplicatesController@scan',
            ]);

            Route::put('confirm/{entry}', [
                'as' => 'entry.duplicates.confirm',
                'uses' => 'ManageDuplicatesController@confirm',
                'middleware' => [
                    LockDuplicateActions::class,
                    DuplicateScanInProgress::class,
                ],
            ]);

            Route::put('unarchive/{entry}', [
                'as' => 'entry.duplicates.unarchive',
                'uses' => 'ManageDuplicatesController@unarchiveNotDuplicate',
                'middleware' => [
                    LockDuplicateActions::class,
                    DuplicateScanInProgress::class,
                ],
            ]);

            Route::put('not-duplicate/{entry}', [
                'as' => 'entry.duplicates.not-duplicate',
                'uses' => 'ManageDuplicatesController@notDuplicate',
                'middleware' => [
                    LockDuplicateActions::class,
                    DuplicateScanInProgress::class,
                ],
            ]);

            Route::put('primary/{entry}', [
                'as' => 'entry.duplicates.primary',
                'uses' => 'ManageDuplicatesController@primary',
                'middleware' => [
                    LockDuplicateActions::class,
                    DuplicateScanInProgress::class,
                ],
            ]);

            Route::put('confirm-group/{entry}', [
                'as' => 'entry.duplicates.confirm-group',
                'uses' => 'ManageDuplicatesController@confirmGroup',
                'middleware' => [
                    LockDuplicateActions::class,
                    DuplicateScanInProgress::class,
                ],
            ]);

            Route::post('duplicate-archive', [
                'as' => 'entry.duplicates.duplicate.archive',
                'uses' => 'ManageDuplicatesController@setDuplicateAndArchive',
                'middleware' => [
                    LockDuplicateActions::class,
                    DuplicateScanInProgress::class,
                ],
            ]);

            Route::get('autocomplete', [
                'as' => 'entry.duplicates.autocomplete',
                'uses' => 'ManageDuplicatesController@autocomplete',
            ]);

            Route::get('compare/{entry}', [
                'as' => 'entry.duplicates.compare',
                'uses' => 'ManageDuplicatesController@compare',
            ]);
        });

        // Manage reviews
        Route::group([
            'prefix' => 'entry/manage-reviews',
            'middleware' => [
                'auth.role',
                'roles:Manager',
                'feature:review_flow',
            ],
        ], function () {
            Route::get('/', [
                'as' => 'review-flow.task.manage',
                'middleware' => 'remembrance',
                'uses' => 'ManageReviewTasksController@index',
            ]);

            Route::delete('/', [
                'as' => 'review-flow.task.manage.delete',
                'uses' => 'ManageReviewTasksController@delete',
            ]);

            Route::put('/', [
                'as' => 'review-flow.task.manage.undelete',
                'uses' => 'ManageReviewTasksController@undelete',
            ]);

            Route::put('/{reviewTask}/resend', [
                'as' => 'review-flow.task.manage.resend-notification',
                'uses' => 'ManageReviewTasksController@resendNotification',
            ]);

            Route::put('/reset', [
                'as' => 'review-flow.task.manage.reset',
                'uses' => 'ManageReviewTasksController@reset',
            ]);

            Route::put('/reassign', [
                'as' => 'review-flow.task.manage.reassign',
                'uses' => 'ManageReviewTasksController@reassign',
            ]);
        });

        // Entries - review
        Route::group([
            'prefix' => 'entry/review',
        ], function () {
            Route::get('/', [
                'as' => 'review-flow.task.index',
                'middleware' => [
                    'auth.user',
                    'remembrance',
                    'feature:review_flow',
                ],
                'uses' => 'ReviewTaskController@index',
            ]);

            Route::get('{reviewTask}', [
                'as' => 'review-flow.task.view',
                'middleware' => [ReviewStageGuestUser::class],
                'uses' => 'ReviewTaskController@view',
            ]);

            Route::put('{reviewTask}', [
                'as' => 'review-flow.task.review',
                'middleware' => [ReviewStageGuestUser::class],
                'uses' => 'ReviewTaskController@review',
            ]);

            Route::put('{reviewTask}/autosave', [
                'as' => 'review-flow.task.autosave',
                'middleware' => [ReviewStageGuestUser::class],
                'uses' => 'ReviewTaskController@autosave',
            ]);

            Route::get('{reviewTask}/complete', [
                'as' => 'review-flow.task.complete',
                'middleware' => [ReviewStageGuestUser::class],
                'uses' => 'ReviewTaskController@reviewCompleted',
            ]);
        });

        // Entry Feedback
        Route::group([
            'namespace' => 'Entry',
            'middleware' => 'auth.role',
        ], function () {
            Route::get('entry/feedback/{entry}', [
                'as' => 'entry.feedback.view',
                'uses' => 'FeedbackController@view',
            ]);
        });

        // Exports
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('export/{exportLayout}/download', [
                'as' => 'export.download',
                'uses' => 'ExportController@download',
            ]);

            Route::get('export/default', [
                'as' => 'export.default',
                'uses' => 'ExportController@downloadDefault',
            ]);
        });

        // Export layouts
        Route::group(['middleware' => ['auth.role', 'feature:custom_exports']], function () {
            Route::get('export', [
                'as' => 'export.index',
                'middleware' => 'remembrance',
                'uses' => 'ExportLayoutController@index',
            ]);

            Route::get('export/new', [
                'as' => 'export.new',
                'uses' => 'ExportLayoutController@new',
            ]);

            Route::post('export/create', [
                'as' => 'export.create',
                'uses' => 'ExportLayoutController@create',
            ]);

            Route::get('export/{exportLayout}', [
                'as' => 'export.edit',
                'uses' => 'ExportLayoutController@edit',
            ]);

            Route::put('export/{exportLayout}', [
                'as' => 'export.update',
                'uses' => 'ExportLayoutController@update',
            ]);

            Route::delete('export/{exportLayout}', [
                'as' => 'export.delete-layout',
                'uses' => 'ExportLayoutController@deleteLayout',
            ]);

            Route::delete('export', [
                'as' => 'export.delete',
                'uses' => 'ExportLayoutController@delete',
            ]);

            Route::put('export', [
                'as' => 'export.undelete',
                'uses' => 'ExportLayoutController@undelete',
            ]);
        });

        // Files
        Route::group(['prefix' => 'file'], function () {
            Route::get('download/{id}', 'FileController@download')
                ->name('file.download');
            Route::get('show/{token}', 'FileController@show')
                ->name('file.show');
            Route::post('upload', 'FileController@upload')->name('file.upload')
                ->middleware(GuestUser::class);
            Route::get('status/{id}', 'FileController@status')
                ->name('file.status')->middleware(GuestUser::class);
            Route::get('metadata/{id}', [
                'as' => 'file.metadata',
                'uses' => 'FileController@metadata',
            ]);
            Route::post('error', 'FileController@error')->name('file.error')
                ->middleware(GuestUser::class);
            Route::delete('own/{file}', 'FileController@deleteOwn')->name('file.own.delete');
        });

        // Funding
        Route::group([
            'prefix' => 'funding',
            'middleware' => ['auth.role', 'feature:fund_management'],
        ], function () {
            // Fund list
            Route::group(['prefix' => 'fund'], function () {
                Route::get('/', [
                    'as' => 'funding.fund.index',
                    'middleware' => 'remembrance',
                    'uses' => 'Funding\FundController@index',
                ]);

                Route::get('new', [
                    'as' => 'funding.fund.new',
                    'uses' => 'Funding\FundController@new',
                ]);

                Route::post('/', [
                    'as' => 'funding.fund.create',
                    'uses' => 'Funding\FundController@create',
                ]);

                Route::get('{fund}', [
                    'as' => 'funding.fund.edit',
                    'uses' => 'Funding\FundController@edit',
                ]);

                Route::put('{fund}', [
                    'as' => 'funding.fund.update',
                    'middleware' => 'outdated',
                    'uses' => 'Funding\FundController@update',
                ]);

                Route::post('copy', [
                    'as' => 'funding.fund.copy',
                    'uses' => 'Funding\FundController@copy',
                ]);

                Route::delete('/', [
                    'as' => 'funding.fund.delete',
                    'uses' => 'Funding\FundController@delete',
                ]);

                Route::put('/', [
                    'as' => 'funding.fund.undelete',
                    'uses' => 'Funding\FundController@undelete',
                ]);
            });

            // Allocate
            Route::group(['prefix' => 'allocation'], function () {
                Route::get('/entries/{entry}', [
                    'as' => 'funding.entry-allocations.index',
                    'uses' => 'Funding\AllocationController@getEntryAllocations',
                ]);

                Route::get('/', [
                    'as' => 'funding.allocation.index',
                    'middleware' => 'remembrance',
                    'uses' => 'Funding\AllocationController@index',
                ]);

                Route::get('/{entry}/{fundAllocation}', [
                    'as' => 'funding.allocation.show',
                    'uses' => 'Funding\AllocationController@show',
                ]);

                Route::post('bulk', [
                    'as' => 'funding.allocation.bulk',
                    'uses' => 'Funding\AllocationController@addBulk',
                ]);

                Route::post('tag', [
                    'as' => 'funding.allocation.tag',
                    'uses' => 'Funding\AllocationController@tag',
                ]);

                Route::delete('untag', [
                    'as' => 'funding.allocation.untag',
                    'uses' => 'Funding\AllocationController@untag',
                ]);

                Route::post('{entry}', [
                    'as' => 'funding.allocation.add',
                    'uses' => 'Funding\AllocationController@add',
                ]);

                Route::put('{entry}', [
                    'as' => 'funding.allocation.update',
                    'uses' => 'Funding\AllocationController@update',
                ]);

                Route::delete('{entry?}', [
                    'as' => 'funding.allocation.delete',
                    'uses' => 'Funding\AllocationController@delete',
                ]);

                Route::put('/', [
                    'as' => 'funding.allocation.undelete',
                    'uses' => 'Funding\AllocationController@undelete',
                ]);
            });
        });

        // Gallery dashboard
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('gallery/{context?}', [
                'as' => 'gallery-dashboard.index',
                'uses' => 'GalleryDashboardController@index',
            ])
                ->where('context', ContextGalleries::getAvailableRouteContexts());

            Route::get('gallery/{scoreSet}/statistics', [
                'as' => 'gallery.score-sets-statistics.index',
                'uses' => 'GalleryDashboardController@getScoreSetStatistics',
            ]);
        });

        // Gallery
        Route::group([
            'middleware' => [
                'roleRegistration',
                'agreement',
                'gallery',
                'judgingModeAllowed',
            ],
        ], function () {
            Route::get('gallery/{scoreSet}', [
                'as' => 'gallery.index',
                'middleware' => 'remembrance',
                'uses' => 'GalleryController@index',
            ]);

            Route::get('gallery/{scoreSet}/{entry}', [
                'as' => 'gallery.view',
                'uses' => 'GalleryController@view',
            ]);

            Route::get('gallery/{scoreSet}/{entry}/next', [
                'as' => 'gallery.next',
                'uses' => 'GalleryController@next',
            ]);

            Route::get('gallery/{scoreSet}/{entry}/previous', [
                'as' => 'gallery.previous',
                'uses' => 'GalleryController@previous',
            ]);

            Route::get('gallery/{scoreSet}/{entry}/slideshow', [
                'as' => 'gallery.slideshow',
                'uses' => 'GalleryController@slideshow',
            ]);

            Route::get('gallery/{scoreSet}/{entry}/slideshow/next', [
                'as' => 'gallery.slideshow.next',
                'uses' => 'GalleryController@nextSlideshow',
            ]);

            Route::get('gallery/{scoreSet}/{entry}/slideshow/previous', [
                'as' => 'gallery.slideshow.previous',
                'uses' => 'GalleryController@previousSlideshow',
            ]);
        });

        // Holocron
        Route::group([], function () {
            Route::post('feature-intro/update-visibility/{id}', 'FeatureIntroController@updateVisibility')
                ->name('feature-intro.update-visibility');
        });

        // Imports
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('import/{import}', [
                'as' => 'import.view',
                'uses' => 'ImportController@view',
            ]);
        });

        // Integrations
        Route::group([
            'middleware' => [
                'auth.role',
            ],
        ], function () {
            Route::get('integration', [
                'as' => 'integration.index',
                'middleware' => 'remembrance',
                'uses' => 'IntegrationController@index',
            ]);

            Route::get('integration/new', [
                'as' => 'integration.new',
                'uses' => 'IntegrationController@new',
            ]);

            Route::post('integration/create', [
                'as' => 'integration.create',
                'middleware' => [
                    SalesforceEnabled::class,
                    CopyleaksEnabled::class,
                ],
                'uses' => 'IntegrationController@create',
            ]);

            Route::get('integration/{integration}', [
                'as' => 'integration.edit',
                'uses' => 'IntegrationController@edit',
            ]);

            Route::put('integration/{integration}', [
                'as' => 'integration.update',
                'uses' => 'IntegrationController@update',
            ]);

            Route::put('integration/{integration}/continue', [
                'as' => 'integration.update.continue',
                'uses' => 'IntegrationController@updateContinue',
            ]);

            Route::delete('integration', [
                'as' => 'integration.delete',
                'uses' => 'IntegrationController@delete',
            ]);

            Route::put('integration', [
                'as' => 'integration.undelete',
                'uses' => 'IntegrationController@undelete',
            ]);

            Route::post('integration/{integration}/clear', [
                'as' => 'integration.clear',
                'uses' => 'IntegrationController@clearSession',
            ]);

            Route::get('integration/{integration}/authenticate', [
                'as' => 'integration.authenticate',
                'uses' => 'IntegrationController@authenticate',
            ]);

            Route::get('integration/{integration}/deauthenticate', [
                'as' => 'integration.deauthenticate',
                'uses' => 'IntegrationController@deauthenticate',
            ]);

            Route::get('{integration}/full-plagiarism-scan', [
                'as' => 'full-plagiarism-scan.confirm',
                'middleware' => ['feature:plagiarism_detection'],
                'uses' => 'PlagiarismScanController@confirmFullPlagiarismScan',
            ]);

            Route::post('{integration}/full-plagiarism-scan', [
                'as' => 'full-plagiarism-scan.start',
                'middleware' => ['feature:plagiarism_detection'],
                'uses' => 'PlagiarismScanController@startFullPlagiarismScan',
            ]);

            Route::get('{integration}/{entry}/plagiarism-scan', [
                'as' => 'entry-plagiarism-scan.start',
                'middleware' => ['feature:plagiarism_detection'],
                'uses' => 'PlagiarismScanController@startEntryPlagiarismScan',
            ]);

            Route::post('plagiarism-scan/{status}/{externalId}', [
                'as' => 'plagiarism-scan.update-status',
                'uses' => 'PlagiarismScanController@updateScanStatus',
            ]);
        });

        // Interface text
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('content/interface', [
                'as' => 'interface-text.index',
                'middleware' => 'remembrance',
                'uses' => 'InterfaceTextController@index',
            ]);

            Route::put('content/interface', [
                'as' => 'interface-text.update',
                'uses' => 'InterfaceTextController@update',
            ]);
        });

        // Judge dashboard
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('judge', [
                'as' => 'judge.index',
                'uses' => 'JudgeDashboardController@index',
            ]);

            Route::get('judge/{scoreSet}/statistics', [
                'as' => 'judge.score-sets-statistics.index',
                'uses' => 'JudgeDashboardController@getScoreSetStatistics',
            ]);
        });

        // Judging dashboard
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('judging/dashboard', [
                'as' => 'judging-dashboard.index',
                'uses' => 'JudgingDashboardController@index',
            ]);
        });

        // Judging fast start
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('judging/fast-start', [
                'as' => 'fast-start.index',
                'uses' => 'JudgingFastStartController@index',
            ]);

            Route::post('judging/fast-start', [
                'as' => 'fast-start.save',
                'uses' => 'JudgingFastStartController@save',
            ]);

            Route::post('judging/fast-start/scoring-criterion/validate', [
                'as' => 'fast-start.scoring-criterion.validate',
                'uses' => 'JudgingFastStartController@validateScoringCriterion',
            ]);

            Route::post('judging/fast-start/round/validate', [
                'as' => 'fast-start.round.validate',
                'uses' => 'JudgingFastStartController@validateRound',
            ]);
        });

        // Judging index
        Route::group([
            'middleware' => [
                'profileComplete',
                'isManager',
                'agreement',
                'allowedEntries',
                'remembrance',
                'judgingModeAllowed',
            ],
        ], function () {
            Route::get('entry/pick', [
                'as' => 'top-pick.index',
                'middleware' => 'roleRegistration',
                'uses' => 'TopPickController@index',
            ]);

            Route::get('entry/qualify', [
                'as' => 'qualifying.index',
                'middleware' => 'roleRegistration',
                'uses' => 'QualifyingController@index',
            ]);

            Route::get('entry/judge', [
                'as' => 'judging.index',
                'middleware' => 'missingScores',
                'uses' => 'JudgingController@index',
            ]);

            Route::get('entry/judge/score-matrix', [
                'as' => 'judging.score.matrix',
                'middleware' => 'missingScores',
                'uses' => 'JudgingController@scoreMatrix',
            ]);
        });

        // Judging
        Route::group([
            'middleware' => [
                'auth.role',
                'allowedEntries',
                'missingScores',
                'agreement',
            ],
        ], function () {
            Route::get('entry/judge/{entry}/next', [
                'as' => 'judging.next',
                'uses' => 'JudgingController@next',
            ]);

            Route::get('entry/judge/{entry}/previous', [
                'as' => 'judging.previous',
                'uses' => 'JudgingController@previous',
            ]);

            Route::get('entry/judge/{entry}/pdf', [
                'as' => 'judging.pdf',
                'middleware' => ['feature:pdfs'],
                'uses' => 'JudgingController@pdf',
            ]);

            Route::get('entry/judge/{entry}', [
                'as' => 'judging.score',
                'uses' => 'JudgingController@score',
            ]);

            Route::put('entry/judge/{entry}', [
                'as' => 'judging.submit',
                'middleware' => [GuestUser::class, 'user.confirmed'],
                'uses' => 'JudgingController@submit',
            ]);

            Route::get('entry/judge/{entry}/slideshow', [
                'as' => 'judging.slideshow',
                'uses' => 'JudgingController@slideshow',
            ]);

            Route::get('entry/judge/{entry}/slideshow/next', [
                'as' => 'judging.slideshow.next',
                'uses' => 'JudgingController@nextSlideshow',
            ]);

            Route::get('entry/judge/{entry}/slideshow/previous', [
                'as' => 'judging.slideshow.previous',
                'uses' => 'JudgingController@previousSlideshow',
            ]);

            Route::post('judging/bulk-download/{scoreSet?}', [
                'as' => 'judging.download',
                'middleware' => AllowedBulkDownload::class,
                'uses' => 'JudgingController@download',
            ]);

            Route::post('judging/tag/{entry}', [
                'as' => 'judging.entry-tag',
                'uses' => 'JudgingController@tagEntry',
            ]);
        });

        // Judging preview
        Route::group([
            'middleware' => [
                'auth.role',
                AllowedPreview::class,
            ],
        ], function () {
            Route::get(
                'entry/judge/preview/{duplicateEntry}/{entry}/{scoreSet?}',
                [
                    'as' => 'entry.judging.preview',
                    'uses' => 'JudgingController@preview',
                ]
            );
        });

        Route::put('entry/judge/{entry}/autosave', [
            'as' => 'judging.autosave',
            'middleware' => [
                'auth.role',
                'missingScores',
                'agreement',
                GuestUser::class,
                'user.confirmed',
            ],
            'uses' => 'JudgingController@autosave',
        ]);

        // Leaderboard
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('judging/leaderboard', [
                'as' => 'leaderboard.index',
                'middleware' => 'remembrance',
                'uses' => 'LeaderboardController@index',
            ]);

            Route::post('judging/leaderboard/recalculate/{scoreSet}', [
                'as' => 'leaderboard.recalculate',
                'uses' => 'LeaderboardController@recalculate',
            ]);

            Route::get('judging/leaderboard/export/score-summary/{type}', [
                'as' => 'leaderboard.export.score-summary',
                'uses' => 'LeaderboardController@exportScoreSummary',
            ]);

            Route::post('judging/leaderboard/download/{scoreSet}', [
                'as' => 'leaderboard.download',
                'uses' => 'LeaderboardController@download',
            ]);

            Route::get('judging/progress', [
                'as' => 'leaderboard.progress',
                'middleware' => 'remembrance',
                'uses' => 'ProgressController@index',
            ]);

            Route::get('judging/progress/export/{type}', [
                'as' => 'leaderboard.progress.export',
                'middleware' => 'remembrance',
                'uses' => 'ProgressController@export',
            ]);
        });

        // Messages
        Route::get('message', [
            'as' => 'message',
            'uses' => 'MessageController@show',
        ]);

        // Notifications
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('notification', [
                'as' => 'notification.index',
                'middleware' => 'remembrance',
                'uses' => 'NotificationController@index',
            ]);

            Route::get('notification/new', [
                'as' => 'notification.new',
                'uses' => 'NotificationController@getNew',
            ]);

            Route::post('notification', [
                'as' => 'notification.create',
                'uses' => 'NotificationController@create',
            ]);

            Route::get('notification/{notification}', [
                'as' => 'notification.edit',
                'uses' => 'NotificationController@edit',
            ]);

            Route::put('notification/{notification}', [
                'as' => 'notification.update',
                'middleware' => 'outdated',
                'uses' => 'NotificationController@update',
            ]);

            Route::delete('notification', [
                'as' => 'notification.delete',
                'uses' => 'NotificationController@delete',
            ]);

            Route::put('notification', [
                'as' => 'notification.undelete',
                'uses' => 'NotificationController@undelete',
            ]);

            Route::post('notification/copy', [
                'as' => 'notification.copy',
                'uses' => 'NotificationController@copy',
            ]);

            Route::post('sms/calculate', [
                'as' => 'sms.calculate',
                'uses' => 'SmsController@calculate',
            ]);
        });

        // Open rounds
        Route::group(
            ['middleware' => ['auth.user', 'roles:Manager']],
            function () {
                Route::get('open-rounds', [
                    'as' => 'open-rounds.form',
                    'uses' => 'OpenRoundsController@form',
                ]);

                Route::post('open-rounds', [
                    'as' => 'open-rounds.update',
                    'uses' => 'OpenRoundsController@update',
                ]);
            }
        );

        // Orders
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('order', [
                'as' => 'order.index',
                'middleware' => 'remembrance',
                'uses' => 'OrderController@index',
            ]);

            Route::get('order/{order}', [
                'as' => 'order.show',
                'uses' => 'OrderController@show',
            ]);

            Route::get('order/invoice/{order}', [
                'as' => 'order.pdf.invoice',
                'uses' => 'OrderController@pdfInvoice',
            ]);

            Route::put('order/{order}', [
                'as' => 'order.update-status',
                'uses' => 'OrderController@updateStatus',
            ]);

            Route::put('order/{order}/notes', [
                'as' => 'order.notes',
                'uses' => 'OrderController@updateNotes',
            ]);

            Route::put('order/{order}/billing-details', [
                'as' => 'order.billing-details',
                'uses' => 'OrderController@updateBillingDetails',
            ]);

            Route::delete('order', [
                'as' => 'order.delete',
                'uses' => 'OrderController@delete',
            ]);

            Route::put('order', [
                'as' => 'order.undelete',
                'uses' => 'OrderController@undelete',
            ]);
        });

        // Panels
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('judging/panel', [
                'as' => 'panel.index',
                'middleware' => 'remembrance',
                'uses' => 'PanelController@index',
            ]);

            Route::get('judging/panel/new', [
                'as' => 'panel.new',
                'uses' => 'PanelController@getNew',
            ]);

            Route::get('judging/panel/judges/{panel?}', [
                'as' => 'panel.judges',
                'uses' => 'PanelController@judges',
            ]);

            Route::post('judging/panel', [
                'as' => 'panel.create',
                'middleware' => 'formRestricted:entry',
                'uses' => 'PanelController@create',
            ]);

            Route::get('judging/panel/{panel}', [
                'as' => 'panel.edit',
                'uses' => 'PanelController@edit',
            ]);

            Route::get('judging/copy-panel/{panel}', [
                'as' => 'panel.copy',
                'uses' => 'PanelController@copy',
            ]);

            Route::put('judging/panel/{panel}', [
                'as' => 'panel.update',
                'middleware' => 'outdated',
                'uses' => 'PanelController@update',
            ]);

            Route::delete('judging/panel', [
                'as' => 'panel.delete',
                'uses' => 'PanelController@delete',
            ]);

            Route::put('judging/panel', [
                'as' => 'panel.undelete',
                'uses' => 'PanelController@undelete',
            ]);
        });

        // Payments
        Route::group([
            'middleware' => [
                'auth.role',
                'trimInput',
                'feature:order_payments',
            ],
        ], function () {
            Route::get('payment/general', [
                'as' => 'payment.general',
                'uses' => 'PaymentSettingsController@general',
            ]);

            Route::put('payment/general', [
                'as' => 'payment.general.update',
                'middleware' => ['feature:order_payments'],
                'uses' => 'PaymentSettingsController@updateGeneral',
            ]);

            Route::get('payment/gateways', [
                'as' => 'payment.gateways',
                'uses' => 'PaymentSettingsController@gateways',
            ]);

            Route::put('payment/gateways', [
                'as' => 'payment.gateways.update',
                'middleware' => ['feature:order_payments'],
                'uses' => 'PaymentSettingsController@updateGateways',
            ]);

            Route::put('payment/toggle', [
                'as' => 'payment.toggle',
                'uses' => 'PaymentSettingsController@toggle',
            ]);

            Route::get('payment/{gateway}/authenticate', [
                'as' => 'payment.authenticate',
                'uses' => 'PaymentSettingsController@authenticate',
            ]);

            Route::get('payment/{gateway}/deauthenticate', [
                'as' => 'payment.deauthenticate',
                'uses' => 'PaymentSettingsController@deauthenticate',
            ]);
        });

        // Payment gateway callbacks
        Route::group(['middleware' => ['auth.user']], function () {
            Route::any('payment/{gateway}/return', [
                'as' => 'payment.gateway.complete',
                'uses' => 'PaymentController@gatewayReturn',
            ]);

            Route::any('payment/{gateway}/cancel', [
                'as' => 'payment.gateway.cancel',
                'uses' => 'PaymentController@gatewayCancel',
            ]);

            Route::get('payment/{gateway}/pending', [
                'as' => 'payment.gateway.pending',
                'uses' => 'PaymentController@pending',
            ]);

            Route::get('payment/{gateway}/status', [
                'as' => 'payment.gateway.status',
                'uses' => 'PaymentController@status',
            ]);
        });

        // Payment methods
        Route::group([
            'prefix' => 'payment-methods',
            'middleware' => ['feature:fund_management', 'auth.role'],
        ], function () {
            Route::get('/', [
                'as' => 'payment-method.index',
                'uses' => 'PaymentMethodController@index',
            ]);
            Route::post('/', [
                'as' => 'payment-method.create',
                'uses' => 'PaymentMethodController@create',
            ]);
            Route::get('new', [
                'as' => 'payment-method.new',
                'uses' => 'PaymentMethodController@add',
            ]);
            Route::get('{paymentMethod}', [
                'as' => 'payment-method.edit',
                'uses' => 'PaymentMethodController@edit',
            ]);
            Route::put('{paymentMethod}', [
                'as' => 'payment-method.update',
                'uses' => 'PaymentMethodController@update',
            ]);
            Route::delete('/', [
                'as' => 'payment-method.delete',
                'uses' => 'PaymentMethodController@delete',
            ]);
            Route::put('/', [
                'as' => 'payment-method.undelete',
                'uses' => 'PaymentMethodController@undelete',
            ]);
        });

        // Payment schedule templates
        Route::group([
            'prefix' => 'payment-schedule-templates',
            'middleware' => ['feature:fund_management', 'auth.role'],
        ], function () {
            Route::get('/', [
                'as' => 'payment-schedule-template.index',
                'uses' => 'PaymentScheduleTemplateController@index',
            ]);
            Route::post('/', [
                'as' => 'payment-schedule-template.create',
                'uses' => 'PaymentScheduleTemplateController@create',
            ]);
            Route::get('new', [
                'as' => 'payment-schedule-template.new',
                'uses' => 'PaymentScheduleTemplateController@add',
            ]);
            Route::get('{paymentScheduleTemplate}', [
                'as' => 'payment-schedule-template.edit',
                'uses' => 'PaymentScheduleTemplateController@edit',
            ]);
            Route::put('{paymentScheduleTemplate}', [
                'as' => 'payment-schedule-template.update',
                'uses' => 'PaymentScheduleTemplateController@update',
            ]);
            Route::delete('/', [
                'as' => 'payment-schedule-template.delete',
                'uses' => 'PaymentScheduleTemplateController@delete',
            ]);
            Route::put('/', [
                'as' => 'payment-schedule-template.undelete',
                'uses' => 'PaymentScheduleTemplateController@undelete',
            ]);
        });

        // Allocation payments
        Route::group([
            'prefix' => 'allocation-payments',
            'middleware' => ['feature:fund_management', 'auth.role'],
        ], function () {
            Route::get('/', [
                'as' => 'allocation-payment.index',
                'middleware' => 'remembrance',
                'uses' => 'AllocationPaymentController@index',
            ]);
            Route::post('/', [
                'as' => 'allocation-payment.create',
                'uses' => 'AllocationPaymentController@create',
            ]);
            Route::put('{allocationPayment}', [
                'as' => 'allocation-payment.update',
                'uses' => 'AllocationPaymentController@update',
            ]);
            Route::delete('/', [
                'as' => 'allocation-payment.delete',
                'uses' => 'AllocationPaymentController@delete',
            ]);
            Route::put('/', [
                'as' => 'allocation-payment.undelete',
                'uses' => 'AllocationPaymentController@undelete',
            ]);
            Route::get('{allocationPayment}/comments', [
                'as' => 'allocation-payment.comments.index',
                'uses' => 'AllocationPaymentController@getComments',
            ]);
            Route::get('/allocations/{fundAllocation}', [
                'as' => 'allocation-payment.allocation.index',
                'uses' => 'AllocationPaymentController@getAllocationPayments',
            ]);
        });

        // Prices
        Route::group(
            ['middleware' => ['auth.role', 'feature:order_payments']],
            function () {
                Route::get('price', [
                    'as' => 'price',
                    'uses' => 'PriceController@index',
                ]);

                Route::get('price/new', [
                    'as' => 'price.new',
                    'uses' => 'PriceController@getNew',
                ]);

                Route::get('price/copy/{price}', 'PriceController@copy')
                    ->name('price.copy');

                Route::post('price', [
                    'as' => 'price.create',
                    'uses' => 'PriceController@create',
                ]);

                Route::get('price/{price}', [
                    'as' => 'price.show',
                    'uses' => 'PriceController@show',
                ]);

                Route::put('price/{price}', [
                    'as' => 'price.update',
                    'middleware' => 'outdated',
                    'uses' => 'PriceController@update',
                ]);

                Route::delete('price/{price}', [
                    'as' => 'price.delete',
                    'uses' => 'PriceController@delete',
                ]);
            }
        );

        // Supported currencies
        Route::group(
            ['middleware' => ['auth.role', 'feature:order_payments']],
            function () {
                Route::get('currency', [
                    'as' => 'currency',
                    'uses' => 'SupportedCurrencyController@index',
                ]);

                Route::get('currency/new', [
                    'as' => 'currency.new',
                    'uses' => 'SupportedCurrencyController@new',
                ]);

                Route::post('currency', [
                    'as' => 'currency.add',
                    'uses' => 'SupportedCurrencyController@add',
                ]);

                Route::delete('currency', [
                    'as' => 'currency.delete',
                    'uses' => 'SupportedCurrencyController@delete',
                ]);

                Route::put('currency', [
                    'as' => 'currency.update',
                    'uses' => 'SupportedCurrencyController@update',
                ]);
            }
        );

        // User profile show/update
        Route::group(['middleware' => 'auth.user'], function () {
            Route::get('profile', 'ProfileController@show')
                ->name('profile.show');
            Route::put('profile', 'ProfileController@update')
                ->name('profile.update')
                ->middleware([ThrottleMobileUpdate::class]);
            Route::get('profile/report', 'ProfileController@report')
                ->name('profile.report');
            Route::get('profile/report/download', 'ProfileController@downloadReport')
                ->name('profile.report.download');
            Route::get('add/role/{role}', 'ProfileController@addRole')
                ->name('add.role');
        });

        // User-specific routes
        Route::group(['middleware' => Authenticate::class], function () {
            // Profile complete (rego step two)
            Route::get('profile/complete', 'ProfileController@complete')
                ->name('profile.complete');
            Route::post('profile/complete', 'ProfileController@updateComplete')
                ->name('registration.complete');
            Route::get(
                'profile/complete/forget',
                'ProfileController@forgetRole'
            )
                ->name('profile.complete.forget');
            Route::get(
                'profile/complete/{registrationRole}',
                'ProfileController@completeRole'
            )
                ->name('profile.complete.role');
            Route::post(
                'profile/complete/{registrationRole}',
                'ProfileController@updateCompleteRole'
            )
                ->name('registration.complete.role');

            // Helpers
            Route::post('profile/ping', 'ProfileController@ping')
                ->name('profile.ping');
            Route::get(
                'profile/resend-token/{user}',
                'ProfileController@resendToken'
            )->name('profile.resend-token');
        });

        Route::post('profile/pusher', 'ProfileController@pusher')
            ->middleware(BroadcastRecipient::class)
            ->name('profile.pusher');

        // /profile/authenticator
        Route::group([
            'middleware' => ['auth.user', 'freshLogin:authenticator.index'],
            'prefix' => 'profile/authenticator',
        ], function () {
            Route::get('/', 'AuthenticatorController@index')
                ->name('authenticator.index');
            Route::post('{adapter}', 'AuthenticatorController@register')
                ->name('authenticator.register');
            Route::delete('{adapter}', 'AuthenticatorController@reset')
                ->name('authenticator.reset');
        });

        // /auth/challenge
        Route::group([
            'middleware' => Authenticate::class,
            'prefix' => 'auth/challenge',
        ], function () {
            Route::get('/', 'Clear\VerificationController@challenge')
                ->name('authenticator.challenge');
            Route::post('{adapter}', 'AuthenticatorController@start')
                ->name('authenticator.start');
            Route::post('/', 'AuthenticatorController@verify')
                ->name('authenticator.verify')->middleware('throttle:10,15');
        });

        // Qualifying
        Route::group([
            'middleware' => [
                'roleRegistration',
                'auth.role',
                'agreement',
                'allowedEntries',
                'judgingModeAllowed',
            ],
        ], function () {
            Route::get('entry/qualify/{scoreSet}/{entry}', [
                'as' => 'qualifying.decide',
                'uses' => 'QualifyingController@decide',
            ]);

            Route::post('entry/qualify/{scoreSet}/{entry}', [
                'as' => 'qualifying.save',
                'middleware' => GuestUser::class,
                'uses' => 'QualifyingController@save',
            ]);

            Route::get('entry/qualify/{scoreSet}/{entry}/next', [
                'as' => 'qualifying.next',
                'uses' => 'QualifyingController@next',
            ]);
            Route::get('entry/qualify/{scoreSet}/{entry}/previous', [
                'as' => 'qualifying.previous',
                'uses' => 'QualifyingController@previous',
            ]);

            Route::get('entry/qualify/{scoreSet}/{entry}/slideshow', [
                'as' => 'qualifying.slideshow',
                'uses' => 'QualifyingController@slideshow',
            ]);

            Route::get('entry/qualify/{scoreSet}/{entry}/slideshow/next', [
                'as' => 'qualifying.slideshow.next',
                'uses' => 'QualifyingController@nextSlideshow',
            ]);

            Route::get('entry/qualify/{scoreSet}/{entry}/slideshow/previous', [
                'as' => 'qualifying.slideshow.previous',
                'uses' => 'QualifyingController@previousSlideshow',
            ]);
        });

        // QR Code
        Route::group(['middleware' => 'auth.user'], function () {
            Route::get('qr/{entry}', [
                'as' => 'qr-code.redirect',
                'uses' => 'QRCodeController@redirect',
            ]);
        });

        // Queue status
        Route::get('queue-status', [
            'as' => 'queue.status',
            'middleware' => ['auth.role', 'jedi:allow'],
            'uses' => 'QueueStatusController@index',
        ]);

        Route::get('account-status', [
            'as' => 'debug.account-status',
            'middleware' => 'jedi:allow',
            'uses' => 'DiagnosticController@accountStatus',
        ]);

        Route::get('remove-account/{accountSlug}/{database}', [
            'as' => 'debug.remove-account',
            'middleware' => 'jedi:allow',
            'uses' => 'DiagnosticController@removeAccount',
        ]);

        Route::group(['middleware' => 'auth.user'], function () {
            Route::get('r/file/{token}', 'RedirectController@file')
                ->name('redirect.file');
            Route::get('redirect/file/{token}', 'RedirectController@file');
        });

        // Registrations
        Route::group([], function () {
            Route::get('/', [
                'as' => 'home',
                'middleware' => [
                    'preferredLanguage',
                    'intendedRedirection',
                ],
                'uses' => 'HomeController@index',
            ]);
        });

        Route::group([
            'prefix' => 'deletion-log',
        ], function () {
            Route::get('/', [
                'as' => 'deletion-log.list',
                'middleware' => 'remembrance',
                'uses' => 'ReportController@list',
            ]);

            Route::get('/{report}', [
                'as' => 'deletion-log.view',
                'uses' => 'ReportController@view',
            ]);

            Route::get('{report}/pdf', [
                'as' => 'deletion-log.pdf',
                'uses' => 'ReportController@pdf',
            ]);
        });

        // Review flow
        Route::group([
            'middleware' => ['auth.role', 'feature:review_flow'],
            'prefix' => 'review-flow',
        ], function () {
            Route::get('/', [
                'as' => 'review-flow.index',
                'middleware' => 'remembrance',
                'uses' => 'ReviewStageController@index',
            ]);

            Route::get('new', [
                'as' => 'review-flow.new',
                'uses' => 'ReviewStageController@getNew',
            ]);

            Route::post('/', [
                'as' => 'review-flow.create',
                'uses' => 'ReviewStageController@create',
            ]);

            Route::get('{reviewStage}', [
                'as' => 'review-flow.edit',
                'uses' => 'ReviewStageController@edit',
            ]);

            Route::put('{reviewStage}', [
                'as' => 'review-flow.update',
                'middleware' => 'outdated',
                'uses' => 'ReviewStageController@update',
            ]);

            Route::delete('/', [
                'as' => 'review-flow.delete',
                'uses' => 'ReviewStageController@delete',
            ]);

            Route::put('/', [
                'as' => 'review-flow.undelete',
                'uses' => 'ReviewStageController@undelete',
            ]);
        });

        // Roles
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('role', [
                'as' => 'role.index',
                'middleware' => 'remembrance',
                'uses' => 'RoleController@index',
            ]);

            Route::group(['middleware' => ['feature:custom_roles']], function () {
                Route::get('role/new', [
                    'as' => 'role.new',
                    'uses' => 'RoleController@getNew',
                ]);

                Route::post('role', [
                    'as' => 'role.create',
                    'uses' => 'RoleController@create',
                ]);
            });

            Route::get('role/{role}', [
                'as' => 'role.edit',
                'uses' => 'RoleController@edit',
            ]);

            Route::put('role/{role}', [
                'as' => 'role.update',
                'middleware' => 'outdated',
                'uses' => 'RoleController@update',
            ]);

            Route::delete('role', [
                'as' => 'role.delete',
                'uses' => 'RoleController@delete',
            ]);

            Route::put('role', [
                'as' => 'role.undelete',
                'uses' => 'RoleController@undelete',
            ]);
        });

        // Contracts
        Route::group(['middleware' => ['auth.role', 'feature:contracts'], 'as' => 'contract.', 'prefix' => 'contract'], function () {
            Route::get('/', [
                'as' => 'index',
                'middleware' => 'remembrance',
                'uses' => 'ContractController@index',
            ]);

            Route::post('download', [
                'as' => 'download',
                'uses' => 'ContractController@download',
            ]);

            Route::delete('delete', [
                'as' => 'delete',
                'uses' => 'ContractController@delete',
            ]);

            Route::put('restore', [
                'as' => 'undelete',
                'uses' => 'ContractController@unDelete',
            ]);

            Route::group(['as' => 'entry.', 'prefix' => 'entry'], function () {
                Route::get('autocomplete', [
                    'as' => 'autocomplete',
                    'uses' => 'ContractController@autocomplete',
                ]);

                Route::get('single/{entrySlug}', [
                    'as' => 'entry',
                    'uses' => 'ContractController@entry',
                ]);
            });
        });

        // Rounds
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('round', [
                'as' => 'round.index',
                'middleware' => 'remembrance',
                'uses' => 'RoundController@index',
            ]);

            Route::get('round/add', [
                'as' => 'round.add',
                'middleware' => 'formRestricted:entry',
                'uses' => 'RoundController@add',
            ]);

            Route::post('round', [
                'as' => 'round.create',
                'uses' => 'RoundController@create',
            ]);

            Route::get('round/{round}', [
                'as' => 'round.edit',
                'uses' => 'RoundController@edit',
            ]);

            Route::put('round/{round}', [
                'as' => 'round.update',
                'middleware' => 'outdated',
                'uses' => 'RoundController@update',
            ]);

            Route::delete('round', [
                'as' => 'round.delete',
                'uses' => 'RoundController@delete',
            ]);

            Route::put('round', [
                'as' => 'round.undelete',
                'uses' => 'RoundController@undelete',
            ]);
        });

        // Score sets
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('judging/score-set', [
                'as' => 'score-set.index',
                'middleware' => 'remembrance',
                'uses' => 'ScoreSetController@index',
            ]);

            Route::get('judging/score-set/new', [
                'as' => 'score-set.new',
                'uses' => 'ScoreSetController@getNew',
            ]);

            Route::post('judging/score-set', [
                'as' => 'score-set.create',
                'middleware' => 'formRestricted:entry',
                'uses' => 'ScoreSetController@create',
            ]);

            Route::post('judging/score-set/copy', [
                'as' => 'score-set.copy',
                'uses' => 'ScoreSetController@copy',
            ]);

            Route::put('judging/score-set/archive', [
                'as' => 'score-set.archive',
                'uses' => 'ScoreSetController@archive',
            ]);

            Route::put('judging/score-set/unarchive', [
                'as' => 'score-set.unarchive',
                'uses' => 'ScoreSetController@unarchive',
            ]);

            Route::get('judging/score-set/{scoreSet}', [
                'as' => 'score-set.edit',
                'uses' => 'ScoreSetController@edit',
            ]);

            Route::put('judging/score-set/{scoreSet}', [
                'as' => 'score-set.update',
                'middleware' => 'outdated',
                'uses' => 'ScoreSetController@update',
            ]);

            Route::delete('judging/score-set', [
                'as' => 'score-set.delete',
                'uses' => 'ScoreSetController@delete',
            ]);

            Route::delete('judging/score-set/{scoreSet}/image/{id}', [
                'as' => 'score-set.delete.image',
                'uses' => 'ScoreSetController@deleteAttachment',
            ]);

            Route::put('judging/score-set', [
                'as' => 'score-set.undelete',
                'uses' => 'ScoreSetController@undelete',
            ]);
        });

        // Scoring criteria
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('judging/scoring-criteria', [
                'as' => 'scoring-criteria.index',
                'middleware' => 'remembrance',
                'uses' => 'ScoringCriteriaController@index',
            ]);

            Route::get('judging/scoring-criteria/new', [
                'as' => 'scoring-criteria.new',
                'uses' => 'ScoringCriteriaController@new',
            ]);

            Route::post('judging/scoring-criteria', [
                'as' => 'scoring-criteria.add',
                'middleware' => 'formRestricted:entry',
                'uses' => 'ScoringCriteriaController@add',
            ]);

            Route::post('judging/scoring-criteria/copy', [
                'as' => 'scoring-criteria.copy',
                'uses' => 'ScoringCriteriaController@copy',
            ]);

            Route::get('judging/scoring-criteria/preview', [
                'as' => 'scoring-criteria.preview',
                'uses' => 'ScoringCriteriaController@preview',
            ]);

            Route::get('judging/scoring-criteria/{scoringCriterion}', [
                'as' => 'scoring-criteria.edit',
                'uses' => 'ScoringCriteriaController@edit',
            ]);

            Route::put('judging/scoring-criteria/{scoringCriterion}', [
                'as' => 'scoring-criteria.update',
                'middleware' => 'outdated',
                'uses' => 'ScoringCriteriaController@update',
            ]);

            Route::delete('judging/scoring-criteria', [
                'as' => 'scoring-criteria.delete',
                'uses' => 'ScoringCriteriaController@delete',
            ]);

            Route::put('judging/scoring-criteria', [
                'as' => 'scoring-criteria.undelete',
                'uses' => 'ScoringCriteriaController@undelete',
            ]);
        });

        // Search
        Route::group(['middleware' => 'auth.user'], function () {
            Route::get('search/settings/{search}', [
                'as' => 'search.settings.load',
                'uses' => 'SearchController@loadSettings',
                'middleware' => 'feature:saved_views',
            ]);

            Route::post('search/settings', [
                'as' => 'search.settings.save',
                'uses' => 'SearchController@saveSettings',
            ]);

            Route::put('search/settings/{search}', [
                'as' => 'search.settings.update',
                'uses' => 'SearchController@updateSettings',
            ]);

            Route::delete('search/settings/{search}', [
                'as' => 'search.settings.delete',
                'uses' => 'SearchController@deleteSettings',
            ]);

            Route::post('search/columns', [
                'as' => 'search.columns.save',
                'uses' => 'SearchController@saveColumns',
            ]);

            Route::post('search/columns/order', [
                'as' => 'search.columns.change-order',
                'uses' => 'SearchController@changeColumnOrder',
            ]);

            Route::post('search/columns/reset', [
                'as' => 'search.columns.reset',
                'uses' => 'SearchController@resetColumns',
            ]);

            Route::get('search/columnator', [
                'as' => 'search.columnator',
                'uses' => 'SearchController@columnator',
            ]);

            Route::get('search/columns', [
                'as' => 'search.columns',
                'uses' => 'SearchController@columns',
            ]);

            Route::get('saved-views/{area?}', [
                'as' => 'search.settings.load-saved-views',
                'uses' => 'SearchController@loadSavedViews',
            ]);
        });

        // Seasons
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('season', [
                'as' => 'season.index',
                'middleware' => 'remembrance',
                'uses' => 'SeasonController@index',
            ]);

            Route::get('season/new', [
                'as' => 'season.new',
                'uses' => 'SeasonController@getNew',
            ]);

            Route::post('season', [
                'as' => 'season.create',
                'uses' => 'SeasonController@create',
            ]);

            Route::get('season/{season}', [
                'as' => 'season.edit',
                'uses' => 'SeasonController@edit',
            ]);

            Route::put('season/{season}', [
                'as' => 'season.update',
                'middleware' => 'outdated',
                'uses' => 'SeasonController@update',
            ]);

            Route::get('season/filterby/{id}', [
                'as' => 'season.filterby',
                'uses' => 'SeasonController@filterby',
            ]);

            Route::get('season/{season}/destroy', [
                'as' => 'season.destroy',
                'uses' => 'SeasonController@destroy',
            ]);

            Route::delete('season/{season}/destroy', [
                'as' => 'season.destroy.confirm',
                'uses' => 'SeasonController@confirmDestroy',
            ]);
        });

        // Settings
        Route::group(['middleware' => ['auth.role', 'trimInput']], function () {
            Route::get('setting/account', [
                'as' => 'setting.account',
                'uses' => 'SettingController@account',
            ]);

            Route::put('setting/account', [
                'as' => 'setting.account.update',
                'uses' => 'SettingController@updateAccount',
            ]);

            Route::get('setting/registration', [
                'as' => 'setting.registration',
                'uses' => 'SettingController@registration',
            ]);

            Route::put('setting/registration', [
                'as' => 'setting.registration.update',
                'uses' => 'SettingController@updateRegistration',
            ]);

            Route::get('setting/entries', [
                'as' => 'setting.entries',
                'uses' => 'SettingController@entries',
            ]);

            Route::put('setting/entries', [
                'as' => 'setting.entries.update',
                'uses' => 'SettingController@updateEntries',
            ]);

            Route::get('setting/languages', [
                'as' => 'setting.languages',
                'uses' => 'SettingController@languages',
            ]);

            Route::put('setting/languages', [
                'as' => 'setting.languages.update',
                'uses' => 'SettingController@updateLanguages',
            ]);

            Route::get('setting/api-v1', [
                'as' => 'setting.api-v1',
                'uses' => 'SettingController@apiV1',
            ]);

            Route::get('setting/social', [
                'as' => 'setting.social',
                'uses' => 'SettingController@social',
            ]);

            Route::put('setting/social', [
                'as' => 'setting.social.update',
                'uses' => 'SettingController@updateSocial',
            ]);

            Route::post('setting/generate-token', [
                'as' => 'setting.generate-token',
                'uses' => 'SettingController@generateToken',
            ]);

            Route::get('setting/domain', 'SettingDomainController@edit')
                ->name('setting.edit-domain');
            Route::get(
                'setting/domain/status',
                'SettingDomainController@status'
            );
            Route::post(
                'setting/domain/status',
                'SettingDomainController@recheck'
            );
            Route::post('setting/domain', 'SettingDomainController@update')
                ->name('setting.update-domain');

            Route::group(['middleware' => 'feature:organisations'], function () {
                Route::get('setting/organisations', [
                    'as' => 'setting.organisations',
                    'uses' => 'SettingController@organisations',
                ]);

                Route::put('setting/organisations', [
                    'as' => 'setting.organisations.update',
                    'uses' => 'SettingController@updateOrganisations',
                ]);
            });
        });

        // Setup guide
        Route::group(['middleware' => ['auth.user', 'roles:ProgramManager']], function () {
            Route::get('guides-and-tours', 'ToursAndGuidesController@index')
                ->name('guides-and-tours.index');

            Route::post('guides-and-tours/reset-guide', 'ToursAndGuidesController@resetGuide')
                ->name('guides.reset');

            Route::post('guides-and-tours/update-guide-checklist', 'ToursAndGuidesController@updateGuideChecklist')
                ->name('guides.update-checklist');

            Route::post('guides-and-tours/update-guide-collapsed', 'ToursAndGuidesController@updateGuideCollapsed')
                ->name('guides.update-collapsed');
        });

        // Subscriptions
        Route::get('subscription/unsubscribe/{token}', [
            'as' => 'subscription.unsubscribe.link',
            'uses' => 'SubscriptionController@unsubscribe',
        ]);

        Route::post('subscription/resubscribe', [
            'as' => 'subscription.resubscribe',
            'uses' => 'SubscriptionController@resubscribe',
        ]);

        // Tags
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('tag', [
                'as' => 'tag.index',
                'middleware' => 'remembrance',
                'uses' => 'TagController@index',
            ]);

            Route::get('tag/new', [
                'as' => 'tag.new',
                'uses' => 'TagController@getNew',
            ]);

            Route::get('tag/autocomplete', [
                'as' => 'tag.autocomplete',
                'uses' => 'TagController@autocomplete',
            ]);

            Route::post('tag', [
                'as' => 'tag.create',
                'uses' => 'TagController@create',
            ]);

            Route::get('tag/{tag}', [
                'as' => 'tag.edit',
                'uses' => 'TagController@edit',
            ]);

            Route::put('tag/{tag}', [
                'as' => 'tag.update',
                'middleware' => 'outdated',
                'uses' => 'TagController@update',
            ]);

            Route::delete('tag', [
                'as' => 'tag.delete',
                'uses' => 'TagController@delete',
            ]);
        });

        Route::get('document', [
            'middleware' => ['auth.user', 'feature:documents'],
            'as' => 'document.user.index',
            'uses' => 'UserDocumentController@index',
        ]);

        // Documents
        Route::group([
            'middleware' => ['auth.role', 'feature:documents'],
            'prefix' => 'setting/document',
            'as' => 'document.',
        ], function () {
            Route::get('/', [
                'as' => 'index',
                'uses' => 'DocumentController@index',
            ]);

            Route::post('/', [
                'as' => 'create',
                'uses' => 'DocumentController@create',
            ]);

            Route::put('{document}', [
                'as' => 'update',
                'middleware' => 'outdated',
                'uses' => 'DocumentController@update',
            ]);

            Route::delete('/', [
                'as' => 'delete',
                'uses' => 'DocumentController@delete',
            ]);

            Route::put('/', [
                'as' => 'undelete',
                'uses' => 'DocumentController@undelete',
            ]);
        });

        // Document templates
        Route::group([
            'middleware' => ['auth.role', 'feature:documents'],
            'prefix' => 'document-template',
            'as' => 'document-template.',
        ], function () {
            Route::get('/', [
                'as' => 'index',
                'uses' => 'DocumentTemplateController@index',
            ]);

            Route::get('new', [
                'as' => 'new',
                'uses' => 'DocumentTemplateController@getNew',
            ]);

            Route::post('/', [
                'as' => 'create',
                'uses' => 'DocumentTemplateController@create',
            ]);

            Route::get('{documentTemplate}', [
                'as' => 'edit',
                'uses' => 'DocumentTemplateController@edit',
            ]);

            Route::put('{documentTemplate}', [
                'as' => 'update',
                'middleware' => 'outdated',
                'uses' => 'DocumentTemplateController@update',
            ]);

            Route::delete('/', [
                'as' => 'delete',
                'uses' => 'DocumentTemplateController@delete',
            ]);

            Route::put('/', [
                'as' => 'undelete',
                'uses' => 'DocumentTemplateController@undelete',
            ]);
        });

        // Taxes
        Route::group(
            ['middleware' => ['auth.role', 'feature:order_payments']],
            function () {
                Route::get('tax', [
                    'as' => 'tax',
                    'uses' => 'TaxController@index',
                ]);

                Route::get('tax/new', [
                    'as' => 'tax.new',
                    'uses' => 'TaxController@getNew',
                ]);

                Route::post('tax', [
                    'as' => 'tax.create',
                    'uses' => 'TaxController@create',
                ]);

                Route::get('tax/{tax}', [
                    'as' => 'tax.edit',
                    'uses' => 'TaxController@edit',
                ]);

                Route::put('tax/{tax}', [
                    'as' => 'tax.update',
                    'middleware' => 'outdated',
                    'uses' => 'TaxController@update',
                ]);

                Route::delete('tax/{tax}', [
                    'as' => 'tax.delete',
                    'uses' => 'TaxController@delete',
                ]);
            }
        );

        // Terms
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('content/term', [
                'as' => 'term.index',
                'middleware' => 'remembrance',
                'uses' => 'TermController@index',
            ]);

            Route::get('content/term/{term}', [
                'as' => 'term.edit',
                'uses' => 'TermController@edit',
            ]);

            Route::put('content/term/{term}', [
                'as' => 'term.update',
                'uses' => 'TermController@update',
            ]);
        });

        // Theme
        Route::group(['middleware' => 'auth.role'], function () {
            Route::get('theme', [
                'as' => 'theme.index',
                'uses' => 'ThemeController@index',
            ]);

            Route::put('theme', [
                'as' => 'theme.update',
                'uses' => 'ThemeController@update',
            ]);
        });

        // Tiers
        Route::group(
            ['middleware' => ['auth.role', 'feature:order_payments']],
            function () {
                Route::get('tier', [
                    'as' => 'tier',
                    'uses' => 'TierController@index',
                ]);

                Route::put('tier', [
                    'as' => 'tier.save',
                    'uses' => 'TierController@save',
                ]);

                Route::get('tier/new', [
                    'as' => 'tier.new',
                    'uses' => 'TierController@getNew',
                ]);

                Route::post('tier', [
                    'as' => 'tier.create',
                    'uses' => 'TierController@create',
                ]);

                Route::get('tier/{tier}', [
                    'as' => 'tier.show',
                    'uses' => 'TierController@show',
                ]);

                Route::put('tier/{tier}', [
                    'as' => 'tier.update',
                    'middleware' => 'outdated',
                    'uses' => 'TierController@update',
                ]);

                Route::delete('tier/{tier}', [
                    'as' => 'tier.delete',
                    'uses' => 'TierController@delete',
                ]);
            }
        );

        // Top pick
        Route::group([
            'middleware' => [
                'roleRegistration',
                'auth.role',
                'agreement',
                'allowedEntries',
                'judgingModeAllowed',
            ],
        ], function () {
            Route::get('entry/pick/{scoreSet}/{entry}', [
                'as' => 'top-pick.show',
                'uses' => 'TopPickController@show',
            ]);

            Route::post('entry/pick/{scoreSet}/{entry}', [
                'as' => 'top-pick.save',
                'middleware' => GuestUser::class,
                'uses' => 'TopPickController@save',
            ]);

            Route::get('entry/pick/{scoreSet}/{entry}/next', [
                'as' => 'top-pick.next',
                'uses' => 'TopPickController@next',
            ]);

            Route::get('entry/pick/{scoreSet}/{entry}/previous', [
                'as' => 'top-pick.previous',
                'uses' => 'TopPickController@previous',
            ]);

            Route::get('entry/pick/{scoreSet}/{entry}/slideshow', [
                'as' => 'top-pick.slideshow',
                'uses' => 'TopPickController@slideshow',
            ]);

            Route::get('entry/pick/{scoreSet}/{entry}/slideshow/next', [
                'as' => 'top-pick.slideshow.next',
                'uses' => 'TopPickController@nextSlideshow',
            ]);

            Route::get('entry/pick/{scoreSet}/{entry}/slideshow/previous', [
                'as' => 'top-pick.slideshow.previous',
                'uses' => 'TopPickController@previousSlideshow',
            ]);
        });

        // Transcoding
        Route::group(['middleware' => 'auth.role'], function () {
            Route::post('transcode/all', [
                'as' => 'transcode.all',
                'uses' => 'TranscodingController@transcodeAll',
            ]);
            Route::post('transcode/{file}/retry', [
                'as' => 'transcode.retry',
                'uses' => 'TranscodingController@retryTranscoding',
            ]);
        });

        // Users
        Route::group([
            'middleware' => [
                'auth.role',
            ],
        ], function () {
            Route::get('user', [
                'as' => 'users.index',
                'middleware' => 'remembrance',
                'uses' => 'UserController@index',
            ]);

            Route::get('agreement/reset/{agreement}', [
                'as' => 'users.reset-agreement',
                'uses' => 'UserController@resetAgreement',
            ]);

            Route::get('users/autocomplete/all', [
                'as' => 'users.autocomplete.all',
                'uses' => 'UserController@autocompleteAll',
            ]);

            Route::get('users/autocomplete/current', [
                'as' => 'users.autocomplete.current',
                'uses' => 'UserController@autocompleteCurrent',
            ]);

            Route::get('users/typeahead', [
                'as' => 'users.typeahead',
                'uses' => 'UserController@typeahead',
            ]);

            Route::get('users/typeahead-program-managers', [
                'as' => 'users.typeahead.program-managers',
                'uses' => 'UserController@typeaheadOnlyProgramManagers',
            ]);

            Route::get('users/new', [
                'as' => 'users.new',
                'uses' => 'UserController@getNew',
            ]);

            Route::get('users/invite', [
                'as' => 'users.invite',
                'uses' => 'UserController@getInvite',
            ]);

            Route::put('user/{user}/reinvite', [
                'as' => 'users.reinvite',
                'uses' => 'UserController@reinvite',
            ]);

            Route::post('users/invite', [
                'as' => 'users.invite.save',
                'uses' => 'UserController@invite',
            ]);

            Route::post('users', [
                'as' => 'users.create',
                'uses' => 'UserController@create',
            ]);

            Route::get('user/import', [
                'as' => 'user.import',
                'uses' => 'UserController@import',
            ]);

            Route::post('user/import', [
                'as' => 'user.import.upload',
                'uses' => 'UserController@uploadImport',
            ]);

            Route::get('users/{user}', [
                'as' => 'users.show',
                'uses' => 'UserController@show',
            ]);

            Route::put('users/{user}', [
                'as' => 'users.update',
                'middleware' => ['outdated', 'canUpdateUser'],
                'uses' => 'UserController@update',
            ]);

            Route::delete('users', [
                'as' => 'user.delete',
                'uses' => 'UserController@delete',
            ]);

            Route::get('user/{user}/destroy', [
                'as' => 'user.destroy',
                'middleware' => 'notAccountOwner',
                'uses' => 'UserController@destroy',
            ]);

            Route::delete('user/{user}/destroy', [
                'as' => 'user.destroy.confirm',
                'middleware' => 'notAccountOwner',
                'uses' => 'UserController@confirmDestroy',
            ]);

            Route::delete('user/bulkDestroy', [
                'as' => 'user.destroy.confirm_bulk',
                'uses' => 'UserController@confirmBulkDestroy',
            ]);

            Route::post('user/role', [
                'as' => 'user.role.assign',
                'uses' => 'UserRoleController@assign',
            ]);

            Route::delete('user/role', [
                'as' => 'user.role.remove',
                'uses' => 'UserRoleController@remove',
            ]);

            Route::put('users', [
                'as' => 'user.undelete',
                'uses' => 'UserController@undelete',
            ]);

            Route::put('user/{user}/confirm', [
                'as' => 'users.confirm',
                'uses' => 'UserController@confirm',
            ]);

            Route::post('user/role', [
                'as' => 'user.role.assign',
                'uses' => 'UserRoleController@assign',
            ]);
            Route::delete('user/role', [
                'as' => 'user.role.remove',
                'uses' => 'UserRoleController@remove',
            ]);

            Route::put('user/{user}/block', [
                'as' => 'users.block',
                'middleware' => 'notAccountOwner',
                'uses' => 'UserController@block',
            ]);

            Route::put('user/{user}/unblock', [
                'as' => 'users.unblock',
                'uses' => 'UserController@unblock',
            ]);

            Route::delete('user/{user}/file/{file}', [
                'as' => 'user.file.delete',
                'uses' => 'UserController@deleteFile',
            ]);

            Route::post('user/download', [
                'as' => 'user.download',
                'uses' => 'UserController@download',
            ]);

            Route::post('user/{user}/emulate', [
                'as' => 'user.emulate',
                'uses' => 'UserController@emulate',
                'middleware' => 'isOwnerOrProgramManager',
            ]);
        });

        // Video Logs
        Route::group(['middleware' => GuestUser::class], function () {
            Route::put('video-log/{file}/log', [
                'as' => 'video-log.log',
                'uses' => 'VideoLogController@log',
            ]);

            Route::post('video-log/{file}/error', [
                'as' => 'video-log.error',
                'uses' => 'VideoLogController@error',
            ]);
        });

        // entry/vote/{scoreSet}
        Route::group([
            'prefix' => 'entry/vote/{scoreSet}',
            'middleware' => [
                'roleRegistration',
                'auth.role',
                'allowedEntries',
                'agreement',
                'judgingModeAllowed',
            ],
        ], function () {
            Route::get('/', [
                'as' => 'voting.index',
                'uses' => 'VotingController@index',
                'middleware' => 'remembrance',
            ]);

            Route::get(
                '{entry}',
                ['as' => 'voting.vote', 'uses' => 'VotingController@vote']
            );
            Route::post('{entry}', [
                'as' => 'voting.vote.save',
                'uses' => 'VotingController@castVote',
                'middleware' => [GuestUser::class, 'user.confirmed'],
            ]);

            Route::get(
                '{entry}/next',
                ['as' => 'voting.next', 'uses' => 'VotingController@next']
            );
            Route::get('{entry}/previous', [
                'as' => 'voting.previous',
                'uses' => 'VotingController@previous',
            ]);

            Route::get('{entry}/slideshow', [
                'as' => 'voting.slideshow',
                'uses' => 'VotingController@slideshow',
            ]);
            Route::get('{entry}/slideshow/next', [
                'as' => 'voting.slideshow.next',
                'uses' => 'VotingController@nextSlideshow',
            ]);
            Route::get('{entry}/slideshow/previous', [
                'as' => 'voting.slideshow.previous',
                'uses' => 'VotingController@previousSlideshow',
            ]);
        });

        // @TODO: Remove these on or after 2017-05-26, when they will definitely no longer be relevant...
        Route::group(
            [
                'middleware' => [
                    'roleRegistration',
                    'auth.role',
                    'allowedEntries',
                    'agreement',
                ],
            ],
            function () {
                Route::get(
                    'entry/vote{scoreSet}/{entry}/next',
                    'VotingController@next'
                );
                Route::get(
                    'entry/vote{scoreSet}/{entry}/previous',
                    'VotingController@previous'
                );
            }
        );

        Route::get('feature-disabled/{feature?}', [
            'as' => 'feature.disabled',
            'uses' => 'FeatureController@disabled',
        ]);

        Route::get('trial-ended', [
            'as' => 'trial.ended',
            'uses' => 'TrialController@ended',
        ]);

        Route::get('zapier', [
            'middleware' => 'feature:api,zapier',
            'as' => 'zapier.embedded',
            'uses' => 'ZapierController',
        ]);

        Route::get('marketplace', [
            'middleware' => 'feature:api,marketplace',
            'as' => 'marketplace.embedded',
            'uses' => 'MarketplaceController@index',
        ]);

        Route::post('marketplace/event', [
            'middleware' => 'feature:api',
            'as' => 'marketplace.event',
            'uses' => 'MarketplaceController@event',
        ]);

        Route::group([
            'prefix' => 'billing',
            'as' => 'billing.',
            'middleware' => ['isNotTrial', 'isOwnerOrProgramManager'],
        ], function () {
            Route::get('/', [
                'as' => 'index',
                'uses' => 'BillingController@index',
            ]);
            Route::group([
                'prefix' => 'contacts',
                'as' => 'contacts.',
            ], function () {
                Route::post('/', [
                    'as' => 'save',
                    'uses' => 'BillingController@upsertContact',
                ]);
                Route::delete('{customerId}/{id}', [
                    'as' => 'delete',
                    'uses' => 'BillingController@deleteContact',
                ]);
            });
            Route::group([
                'prefix' => 'invoices',
                'as' => 'invoices.',
                'middleware' => ['isOwner'],
            ], function () {
                Route::put('/organisation-name', [
                    'as' => 'organisation-name',
                    'uses' => 'BillingController@updateOrganisationName',
                ]);
                Route::put('/organisation-address', [
                    'as' => 'organisation-address',
                    'uses' => 'BillingController@updateOrganisationAddress',
                ]);
                Route::post('/payment-method', [
                    'as' => 'payment-method',
                    'uses' => 'BillingController@createPaymentMethod',
                ]);
                Route::put('/payment-method', [
                    'as' => 'payment-method.update',
                    'uses' => 'BillingController@updatePaymentMethod',
                ]);
                Route::get('/billing-history-link/{customerId}', [
                    'as' => 'billing-history-link',
                    'uses' => 'BillingController@generateBillingHistoryLink',
                ]);
                Route::get('/download/{invoiceId}', [
                    'as' => 'pdf',
                    'uses' => 'BillingController@invoicePdf',
                ]);
            });
            Route::group([
                'prefix' => 'subscriptions',
                'as' => 'subscriptions.',
                'middleware' => ['isOwner'],
            ], function () {
                Route::put('/upgrade-plan', [
                    'as' => 'upgrade-plan',
                    'uses' => 'BillingController@upgradePlan',
                ]);
                Route::put('/delete-account', [
                    'as' => 'delete-account',
                    'uses' => 'BillingController@deleteAccount',
                ]);
            });
        });

        Route::group([
            'prefix' => 'account',
            'middleware' => ['isOwner'],
        ], function () {
            Route::post('/change-owner/{owner}', [
                'as' => 'account.update-owner',
                'uses' => 'AccountController@changeAccountOwner',
            ]);
        });

        Route::group([
            'prefix' => 'grant',
            'middleware' => ['feature:grants', 'auth.role'],
        ], function () {
            Route::group([
                'prefix' => 'manager',
            ], function () {
                Route::get('/', [
                    'as' => 'grant.manager.index',
                    'middleware' => 'remembrance',
                    'uses' => 'ManageGrantsController@index',
                ]);
                Route::post('download', [
                    'as' => 'grant.manager.download',
                    'uses' => 'ManageGrantsController@download',
                ]);
            });
            Route::group([
                'prefix' => 'status',
            ], function () {
                Route::get('/', [
                    'as' => 'grant.status.index',
                    'middleware' => 'remembrance',
                    'uses' => 'GrantStatusController@index',
                ]);
                Route::post('/', [
                    'as' => 'grant.status.create',
                    'uses' => 'GrantStatusController@create',
                ]);
                Route::get('new', [
                    'as' => 'grant.status.new',
                    'uses' => 'GrantStatusController@new',
                ]);
                Route::get('{grantStatus}', [
                    'as' => 'grant.status.edit',
                    'uses' => 'GrantStatusController@edit',
                ]);
                Route::put('{grantStatus}', [
                    'as' => 'grant.status.update',
                    'uses' => 'GrantStatusController@update',
                ]);
                Route::delete('/', [
                    'as' => 'grant.status.delete',
                    'uses' => 'GrantStatusController@delete',
                ]);
                Route::put('/', [
                    'as' => 'grant.status.undelete',
                    'uses' => 'GrantStatusController@undelete',
                ]);
                Route::post('add/{grantStatus?}', [
                    'as' => 'grant.status.add-to-entries',
                    'uses' => 'GrantStatusController@addToEntries',
                ]);
            });
        });

        // Releases
        Route::group([], function () {
            Route::get('updates', [
                'as' => 'updates.index',
                'uses' => 'UpdatesController@index',
            ]);
            Route::get('updates/unread-count', [
                'as' => 'updates.unread.count',
                'uses' => 'UpdatesController@unreadCount',
            ]);
        });

        // Context menu
        Route::put('context-menu/selected/{context}', [
            'as' => 'context.status',
            'uses' => 'ContextMenuController@updateSelectedContext',
        ])->where('context', implode('|', app(ContextMenuService::class)->contextNames()));
    });
    Route::get('user/stop-emulation', [
        'as' => 'user.stop-emulation',
        'uses' => 'UserController@stopEmulation',
    ]);
});

// SAML
Route::group([
    'middleware' => ['httpsRedirect', 'feature:saml', Saml::class],
    'namespace' => 'Auth',
    'prefix' => 'saml',
], function () {
    Route::get('login', [
        'as' => 'saml.login',
        'uses' => 'SamlController@login',
    ]);

    Route::post('callback', [
        'as' => 'saml.callback',
        'uses' => 'SamlController@callback',
    ]);

    Route::get('metadata', [
        'as' => 'saml.metadata',
        'uses' => 'SamlController@metadata',
    ]);

    Route::get('authenticate', [
        'as' => 'saml.authenticate',
        'middleware' => ['consumer', 'isTempConsumer', 'verification'],
        'uses' => 'SamlController@authenticate',
    ]);
});

// Redirector
Route::get('r/{token}', 'RedirectController@token')
    ->name('redirect.token');
Route::get('redirect/{token}', 'RedirectController@token');

Route::get('favicon.ico', 'AssetController@favicon')->name('favicon.ico');
Route::get('favicon-logo', 'AssetController@faviconLogo')->name('favicon-logo');

Route::post('bulk-download/update-status/{file}', [
    'as' => 'bulk-download-error',
    'uses' => 'BulkDownloadController@updateStatus',
]);

Route::get('sitemap.xml', [
    'as' => 'sitemap',
    'uses' => 'SitemapController@sitemap',
]);
