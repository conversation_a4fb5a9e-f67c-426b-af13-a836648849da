<?php

namespace AwardForce\Library\Authorization;

use AwardForce\Library\Facades\CurrentLocale;
use AwardForce\Modules\Identity\Roles\Contracts\AuthoriserInterface;
use AwardForce\Modules\Identity\Roles\Contracts\PermissionRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\PermissionCollection;
use AwardForce\Modules\Identity\Roles\Services\PermissionResourcesService;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Services\Emulation\UserEmulator;
use Illuminate\Queue\Attributes\WithoutRelations;
use Platform\Authorisation\Consumer as Contract;
use Platform\Authorisation\FeatureRoles\ProgramManager;
use Platform\Authorisation\Signatory;
use Platform\Authorisation\Signature;
use Platform\Bus\SerializesModels;
use Platform\Language\Language;

/**
 * Class ConsumerManager
 *
 * Simple value object for user with the AuthenticatedConsumer class.
 *
 * <AUTHOR>
 */
#[WithoutRelations]
class UserConsumer implements AuthoriserInterface, Contract, Signatory
{
    use SerializesModels;

    /**
     * The User object reflecting the user that is currently accessing the application. This object is
     * not accessible externally. The reason for this is because we want both user and api consumers to
     * be accessed and dealt with in the same way. As a result, you can return the id for the consumer,
     * then query for a user based on that id if you require a user object. It is better, however - to
     * simply not think of actions performed against the API as a user.
     *
     * @var User
     */
    private $user;

    /** @var int */
    private $userId;

    /** @var int */
    private $accountId;

    /**
     * Caches the user's permissions for the request.
     *
     * @var PermissionCollection
     */
    private $permissions;

    /**
     * @var PermissionRepository
     */
    private $permissionRepository;

    /**
     * isOwner property value cache, to prevent multiple identical queries.
     *
     * @var bool
     */
    private $isOwner;

    /**
     * Cached user Language.
     *
     * @var Language
     */
    private $language;

    /**
     * @var RoleRepository
     */
    private $roles;

    /**
     * Constructs the class and sets the $id property.
     */
    public function __construct(
        User $user,
        ?PermissionRepository $permissionRepository = null,
        ?RoleRepository $roles = null
    ) {
        $this->user = $user;
        $this->userId = $this->user->id;
        $this->accountId = current_account_id();
        $this->permissionRepository = $permissionRepository ?: app(PermissionRepository::class);
        $this->roles = $roles ?: app(RoleRepository::class);
        $this->permissions = $this->setupPermissions($user, $this->accountId);
    }

    /**
     * Determines whether the user has access to the given action and resource.
     *
     * @param  string  $action
     * @param  string  $resource
     * @return bool
     */
    public function can($action, $resource)
    {
        // Owners can always view all resources on the system
        if ($this->isOwner()) {
            return true;
        }

        if (! $this->emulatorPermitted($action, $resource)) {
            return false;
        }

        $permitted = false;

        if (! is_null($permissions = $this->permissions->matchAll($resource, $action))) {
            foreach ($permissions as $permission) {
                if ($permission->allowed()) {
                    $permitted = true;
                } elseif ($permission->denied()) {
                    return false; // Deny always trumps allow if set by any role
                }
            }
        }

        return $permitted;
    }

    /**
     * Return true if the consumer has permission to perform $action
     * upon $resource.
     *
     * @param  string  $action
     * @param  string  $resource
     * @return mixed
     */
    public function cannot($action, $resource)
    {
        return ! $this->can($action, $resource);
    }

    /**
     * Returns true if the user is the owner of the current account/request.
     *
     * @return bool
     */
    public function isOwner()
    {
        $this->isOwner = $this->isOwner ?: current_account()->userId == $this->user->id;

        return $this->isOwner;
    }

    /**
     * Returns an array of accounts that the user can manage or contribute to.
     *
     * @return Collection
     */
    public function accounts()
    {
        return $this->user->accounts;
    }

    /**
     * Returns the id for the user accessing the application.
     *
     * @return int
     */
    public function id()
    {
        return $this->user->id;
    }

    /**
     * Returns the language that the consumer prefers.
     */
    public function language(): Language
    {
        return $this->user->preferredLanguage();
    }

    /**
     * Returns the account default language if the user's language is not active.
     */
    public function strictLanguage(): Language
    {
        return CurrentLocale::strictDefault();
    }

    /**
     * Type is user.
     *
     * @return ConsumerType
     */
    public function type()
    {
        return 'user';
    }

    /**
     * Setup the permissions to be used for future calls.
     *
     * @param  int  $accountId
     * @return mixed
     */
    protected function setupPermissions(User $user, $accountId)
    {
        return $this->permissionRepository->getByAccountAndUserId($accountId, $user->id);
    }

    /**
     * Returns the roles the consumer has been assigned.
     *
     * @return \Illuminate\Support\Collection
     */
    public function roles()
    {
        return $this->roles->requestCache()->getRolesForUser($this->user->id);
    }

    /**
     * Returns the user model for the consumer.
     *
     * @return \AwardForce\Modules\Identity\Users\Models\User
     */
    public function user()
    {
        return $this->user;
    }

    public function globalUser()
    {
        return $this->user->globalUser;
    }

    /**
     * Return the preferred timezone of the user.
     *
     * @return mixed
     */
    public function timezone()
    {
        return $this->user()->currentMembership ? $this->user()->currentMembership->timezone : setting('timezone');
    }

    /**
     * Returns the name of the consumer.
     */
    public function name(): string
    {
        return $this->user()->fullName();
    }

    /**
     * Unique signature of the implementing entity.
     */
    public function signature(): Signature
    {
        return new Signature('af4', 'user', $this->id(), $this->user()->email);
    }

    /**
     * Return true if the user has a role that requires authenticator challenge on login.
     */
    public function authenticatorRequired(): bool
    {
        return $this->globalUser()->requireAuthenticator
            || $this->roles()->contains('requireAuthenticator', true);
    }

    /**
     * @return string[]
     */
    public function completeSleep(): array
    {
        return ['userId', 'accountId'];
    }

    public function completeWakeup()
    {
        $this->user = app(UserRepository::class)->requireById($this->userId);
        $this->permissionRepository = app(PermissionRepository::class);
        $this->roles = app(RoleRepository::class);
        $this->permissions = $this->setupPermissions($this->user, $this->accountId);
    }

    /**
     * {@inheritDoc}
     */
    public function globalId(): ?string
    {
        return $this->user ? (string) $this->user->globalId : null;
    }

    /**
     * {@inheritDoc}
     */
    public function globalAccountId(): ?string
    {
        return (string) current_account_global_id();
    }

    private function emulatorPermitted($action, $resource): bool
    {
        if (UserEmulator::active() && app(PermissionResourcesService::class)->isSensitiveResource($resource)) {
            $emulator = UserEmulator::emulatingUser();
            if ($emulator?->id !== $this->id()) {
                return (new self($emulator))->can($action, $resource);
            }
        }

        return true;
    }

    public function isChapterLimited(): bool
    {
        return ! $this->isOwner() && ! ProgramManager::appliesTo($this) && $this->roles()->where('chapterLimited', true)->isNotEmpty();
    }
}
