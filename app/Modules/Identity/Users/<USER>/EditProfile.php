<?php

namespace AwardForce\Modules\Identity\Users\View;

use AwardForce\Library\Facades\CurrentLocale;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\Uploadable;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Services\ProfilePhotoUploadValidator;
use Consumer;
use Illuminate\Http\Request;
use Platform\View\View;

class EditProfile extends View
{
    use Uploadable;

    public function __construct(
        private Request $request,
        private UserRepository $users,
        private ProfilePhotoUploadValidator $validator,
        private FileRepository $files
    ) {
        $this->registerTranslations();
        $this->registerRoutes();
    }

    private function registerTranslations(): void
    {
        VueData::registerTranslations([
            'files.buttons.drag_and_drop',
            'files.buttons.size_download',
            'files.actions.cancel',
            'files.actions.delete',
            'files.actions.upload',
            'files.status.processing',
            'files.buttons.single',
            'files.status',
            'miscellaneous.search.or',
            'users.form.color',
            'users.form.photo',
            'buttons.on',
            'buttons.off',
            'auth.authenticator.title',
            'auth.authenticator.configure',
        ]);
    }

    public function user(): User
    {
        return $this->request->user()->append('color');
    }

    public function preferredLanguage(): string
    {
        return $this->users->getPreferredLanguage($this->user(), current_account_id()) ?: CurrentLocale::code();
    }

    public function uploadOptions(): string
    {
        return json_encode($this->validator->setupUploader($this->setupUploader())->options());
    }

    public function translations(): array
    {
        return translations_for_vue(Consumer::languageCode(), [
            'files.buttons.drag_and_drop',
            'files.buttons.size_download',
            'files.actions.cancel',
            'files.actions.delete',
            'files.actions.upload',
            'files.status.processing',
            'files.buttons.single',
            'files.status',
            'miscellaneous.search.or',
            'users.form.color',
            'users.form.photo',
        ]);
    }

    public function avatar(): string
    {
        return json_encode($this->mapFile($this->user()->profilePhotoFile));
    }

    private function mapFile(?File $file): array
    {
        return [
            'id' => $file?->id,
            'remoteId' => $file?->id,
            'slug' => $file ? (string) $file->slug : null,
            'token' => $file->token ?? null,
            'file' => $file?->file,
            'original' => $file?->original,
            'size' => $file?->size,
            'mime' => $file?->mime,
            'status' => $file?->status,
            'transcodingStatus' => $file?->transcodingStatus,
            'url' => $file ? cloud_asset_url($file->file, true) : null,
            'image' => $this->user()->profilePhoto()->image(480, 480),
            'resource' => $file?->resource,
        ];
    }

    private function registerRoutes()
    {
        VueData::registerRoutes([
            'file.own.delete',
        ]);
    }
}
