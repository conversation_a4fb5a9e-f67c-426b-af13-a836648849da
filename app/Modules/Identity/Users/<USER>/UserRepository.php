<?php

namespace AwardForce\Modules\Identity\Users\Contracts;

use AwardForce\Library\Database\Repository\Builder\WithSlugBuilderRepository;
use AwardForce\Modules\Accounts\Contracts\AccountInterface;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Exports\Models\ExportRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Models\User;
use Illuminate\Support\Collection;
use Platform\Authorisation\ValueObjects\Permission;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Repository;

interface UserRepository extends BuilderRepository, ExportRepository, Repository, WithSlugBuilderRepository
{
    /**
     * Should return a user object based on the email address.
     *
     * @param  string  $email
     * @return object|null
     */
    public function getByEmail($email);

    /**
     * Return a bunch of users based on the is provided - for export.
     *
     * @return Collection
     */
    public function getForExport(array $userIds);

    /**
     * Should return a user object based on the email address.
     *
     * @param  string  $email
     * @param  string  $mobile
     * @return User|null
     */
    public function getByEmailOrMobile($email, $mobile);

    /**
     * Retrieve all user records that match the emails provided.
     *
     * @return mixed
     */
    public function getAllByEmails(array $emails);

    /**
     * Retrieve all user records that match any of the emails or mobiles provided.
     *
     * @return mixed
     */
    public function getAllByEmailOrMobile(array $emails, array $mobiles);

    /**
     * Same as getByEmail, but also allows for restriction by account id.
     *
     * @param  string  $email
     * @param  AccountInterface|Account  $account
     * @return User
     */
    public function getByEmailAndAccount($email, $account);

    /**
     * Search for a user by mobile number, allows for restriction by account id.
     *
     * @param  string  $mobile
     * @param  AccountInterface|Account  $account
     * @return User
     */
    public function getByMobileAndAccount($mobile, $account);

    /**
     * Search for a list of users based on name.
     *
     * @param  string  $name
     * @param  int  $limit
     * @param  bool  $restrict Whether to restrict the user selection to the current account or not.
     * @return mixed
     */
    public function getAllByName($name, $limit = 8, $restrict = true);

    /**
     * Search for a list of users with assignments based on name.
     *
     * @param  string  $name
     * @param  int  $limit
     * @return mixed
     */
    public function getAllWithAssignmentsByName($name, $limit = 8);

    /**
     * Return a list of users that have been assigned a specific role.
     *
     * @return mixed
     */
    public function getByRole(Role $role);

    /**
     * Return a list of users that have been assigned a specific role.
     *
     * @return mixed
     */
    public function getByRoles(array $roleIds);

    /**
     * Assigns a user to a given role.
     *
     * @param  object  $user
     * @param  int  $roleId
     * @return mixed
     */
    public function assignToRole($user, $roleId);

    /**
     * Unassign a role from a user.
     *
     * @param  object  $user
     * @param  int  $roleId
     * @return mixed
     */
    public function unassignRole($user, $roleId);

    /**
     * Sets the preferred language for the user.
     *
     * @param  int  $accountId
     * @param  string  $language
     * @return mixed
     */
    public function setPreferredLanguage(User $user, $accountId, $language);

    /**
     * Get the preferred language for the required user and account.
     *
     * @param  int  $accountId
     * @return mixed
     */
    public function getPreferredLanguage(User $user, $accountId);

    /**
     * Get the current language for the required user.
     *
     * @return mixed
     */
    public function getCurrentLanguage(User $user);

    /**
     * Counts the accounts that the user is a member of.
     *
     * @return int
     */
    public function countAccounts(User $user);

    /**
     * Return a collection of users that have been assigned the allowed roles, and
     * have not been assigned any of the denied roles.
     *
     * @return mixed
     */
    public function getWithinRoles(array $allowedRoleIds, array $deniedRoleIds);

    /**
     * Returns all of the active users for the provided account, or the current account if one isn't provided.
     *
     * @param  int  $accountId
     * @return Collection
     */
    public function getAllForAccount($accountId = null);

    /**
     * Returns all of the trashed users for the provided account, or the current account if one isn't provided.
     *
     * @param  int  $accountId
     * @return Collection
     */
    public function getTrashedForAccount($accountId = null);

    /**
     * Returns the user, if they are a member of the current account.
     *
     * @param  int  $userId
     * @param  bool  $withTrashed
     * @param  int  $accountId
     * @return User
     */
    public function getInAccount($userId, $withTrashed = false, $accountId = null);

    /**
     * Returns an array of UserIds for the specified account.
     *
     * @param  int  $accountId
     * @return array
     */
    public function getIdsForAccount($accountId = null, array $limitTo = []);

    /**
     * Retrieves the user with the given auth_token, optionally clearing out the token so it cannot be reused.
     *
     * @param  string  $token
     * @param  bool  $clear
     * @return User
     */
    public function requireByAuthToken($token, $clear = true);

    /**
     * Retrieve the total number of new users for the given time period.
     *
     * @param  int  $amount
     * @param  string  $period Eg. days, hours, weeks, months
     * @param  int  $offset Number of period to offset by. For example: 24 if period is set to hours.
     * @return mixed
     */
    public function getNewUserTotal($amount, $period, $offset = 0);

    /**
     * Retrieve the total amount of unconfirmed users at a point in time.
     *
     * @param  int  $amount The number of units back in time
     * @param  string  $unit Eg. hours, days, weeks, months
     * @return mixed
     */
    public function getUnconfirmedTotal($amount = 0, $unit = null);

    /**
     * Retrieve the total amount of users with at least one entry at a point in time.
     *
     * @param  int  $seasonId
     * @param  int  $amount The number of units back in time
     * @param  string  $unit Eg. hours, days, weeks, months
     * @return mixed
     */
    public function getWithEntriesTotal($seasonId, $amount = 0, $unit = null);

    /**
     * Retrieve the total amount of users with no entries at a point in time.
     *
     * @param  int  $seasonId
     * @param  int  $amount The number of units back in time
     * @param  string  $unit Eg. hours, days, weeks, months
     * @return mixed
     */
    public function getWithoutEntriesTotal($seasonId, $amount = 0, $unit = null);

    /**
     * @return void
     */
    public function updateCookiePreferences(User $user, array $cookies);

    /**
     * @param  string  $issuer
     * @param  string  $nameId
     * @return mixed
     */
    public function getSamlUser($issuer, $nameId);

    /**
     * @param  string  $issuer
     * @param  string  $userId
     * @return mixed
     */
    public function getSamlUserById($issuer, $userId);

    /**
     * @param  string  $issuer
     * @param  string  $nameId
     * @return void
     */
    public function updateSamlUser(User $user, $issuer, $nameId);

    /**
     * Destroy all non-guest user records that have no associated memberships.
     *
     * @return mixed
     */
    public function destroyOrphaned(bool $dry = false);

    /**
     * @return Collection
     */
    public function getWithoutSlug();

    /**
     * Retrieves user by its Global Id
     *
     * @return mixed
     */
    public function getByGlobalId($globalId);

    /**
     * Should return a user object based on the mobile
     *
     * @param  string  $mobile
     * @return object|null
     */
    public function getByMobile($mobile);

    /**
     * @return mixed
     */
    public function refreshUser(User $user);

    /**
     * Get all users with chapter limited role(s).
     */
    public function getChapterManagers();

    public function joinRoles(bool $withTrashed = false): self;

    public function joinPermissions(bool $withTrashed = false): self;

    public function allowedWith(Permission ...$permission): self;
}
