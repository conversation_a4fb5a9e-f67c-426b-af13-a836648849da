<?php

namespace AwardForce\Modules\Identity\Users\Repositories;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Library\Database\Repository\Builder\HasSlugBuilder;
use AwardForce\Modules\Accounts\Contracts\AccountInterface;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Exports\Models\Exportable;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;
use Platform\Authorisation\ValueObjects\Permission;
use Platform\Database\Eloquent\Collection;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentUserRepository extends Repository implements UserRepository
{
    use Exportable;
    use HasQueryBuilder;
    use HasSlugBuilder;

    /**
     * Users are not technically restricted by account.
     *
     * @var bool
     */
    public $restrictByAccount = false;

    public function __construct(User $user)
    {
        $this->model = $user;
    }

    /**
     * Retrieve a user based on the email. This is also restricted by the current account. If
     * no user exists by that email address that is also associated with the account,
     *
     * @param  string  $email
     * @return User
     */
    public function getByEmail($email)
    {
        return $this->getBy('email', $email)->first();
    }

    /**
     * Retrieve all user records that match the emails provided.
     *
     * @return mixed
     */
    public function getAllByEmails(array $emails)
    {
        return $this->getQuery()
            ->with('roles', 'accounts')
            ->whereIn('email', $emails)
            ->get();
    }

    /**
     * Retrieve all user records that match any of the emails or mobiles provided.
     *
     * @return mixed
     */
    public function getAllByEmailOrMobile(array $emails, array $mobiles)
    {
        return $this->getQuery()
            ->select('id', 'slug', 'first_name', 'last_name', 'email', 'mobile', 'global_id')
            ->whereIn('email', $emails)
            ->orWhereIn('mobile', $mobiles)
            ->get();
    }

    /**
     * Return a single model object based on its url slug value.
     *
     * @param  string  $slug
     * @return null
     */
    public function getBySlug($slug)
    {
        return $this->getByQuery('slug', $slug)
            ->first();
    }

    /**
     * Return a list of users that have been assigned a specific role.
     *
     * @return mixed
     */
    public function getByRole(Role $role)
    {
        return $this->getQuery()->whereHas('roles', function ($query) use ($role) {
            $query->where('roles.id', '=', $role->id);
        })->get();
    }

    /**
     * Return a list of users that have been assigned to at least one of the received roles.
     *
     * @return mixed
     */
    public function getByRoles(array $roleIds)
    {
        return $this->getQuery()->whereHas('roles', function ($query) use ($roleIds) {
            $query->whereIn('roles.id', $roleIds);
        })->get();
    }

    /**
     * Same as getByEmail, but also allows for restriction by account id.
     *
     * @param  string  $email
     * @param  AccountInterface|Account  $account
     * @return User
     */
    public function getByEmailAndAccount($email, $account)
    {
        return $this->getMembershipQuery($account)
            ->distinct()
            ->select('users.id', 'users.email')
            ->whereEmail($email)
            ->first();
    }

    /**
     * Search for a user by mobile number, allows for restriction by account id.
     *
     * @param  string  $mobile
     * @param  AccountInterface|Account  $account
     * @return User
     */
    public function getByMobileAndAccount($mobile, $account)
    {
        return $this->getMembershipQuery($account)
            ->distinct()
            ->select('users.id', 'users.mobile')
            ->whereMobile($mobile)
            ->first();
    }

    /**
     * Restrict the query by account, return a new query object.
     *
     * @param  AccountInterface  $account
     * @param  bool  $active
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function getMembershipQuery($account, $active = false)
    {
        $query = $this->getQuery()
            ->join('memberships', 'memberships.user_id', '=', 'users.id')
            ->where('memberships.account_id', '=', $account->getId());

        if ($active) {
            $query->whereNull('memberships.deleted_at');
        }

        return $query;
    }

    /**
     * Get a list of account ids with account name a user belongs to.
     *
     * @param  null  $queryString
     * @return array
     */
    public function getAccounts($user, $queryString = null)
    {
        $userRecord = $this->getQuery()->where('id', '=', $user->id)->first();

        $results = [];

        $accounts = $userRecord->accounts()->whereNull('pivot_deleted_at')->get();

        foreach ($accounts as $account) {
            $nameTranslation = $account->translations()
                ->where('field', '=', 'name')
                ->where('value', 'LIKE', "%{$queryString}%")
                ->first();

            if ($nameTranslation['value']) {
                $results[] = ['id' => $account->id, 'text' => $nameTranslation['value']];
            }
        }

        return $results;
    }

    /**
     * Search by name, return query object.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function searchByName($query, $name)
    {
        return $query->where(function ($query) use ($name) {
            // Split the name so we can do more specific searches
            $splitName = explode(' ', $name);

            if (count($splitName) == 1) {
                $query->orWhere('first_name', 'LIKE', '%'.$splitName[0].'%');
                $query->orWhere('last_name', 'LIKE', '%'.$splitName[0].'%');
            } elseif (count($splitName) == 2) {
                $query->where(function ($query) use ($splitName) {
                    $query->where('first_name', 'LIKE', '%'.$splitName[0].'%')
                        ->where('last_name', 'LIKE', '%'.$splitName[1].'%');
                })->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", '%'.$name.'%');
            } else {
                $query->whereRaw("CONCAT(first_name, ' ', last_name) like ?", '%'.$name.'%');
            }
        });
    }

    /**
     * Search for a list of users based on name.
     *
     * @param  string  $name
     * @param  int  $limit
     * @param  bool  $restrict Whether to restrict the user selection to the current account or not.
     * @return mixed
     */
    public function getAllByName($name, $limit = 8, $restrict = true)
    {
        $query = ($restrict) ?
            $this->getMembershipQuery(current_account(), true) :
            $this->getQuery();

        $query->distinct()
            ->select('users.id', 'users.slug', 'users.email', 'users.mobile', 'users.first_name', 'users.last_name');

        return $this->searchByName($query, $name)
            ->take($limit)
            ->get();
    }

    /**
     * Search for a list of users with assignments based on name.
     *
     * @param  string  $name
     * @param  int  $limit
     * @return mixed
     */
    public function getAllWithAssignmentsByName($name, $limit = 8)
    {
        $query = $this->getMembershipQuery(current_account());

        $query->distinct()
            ->select('users.slug', 'users.id', 'users.email', 'users.mobile', 'users.first_name', 'users.last_name')
            ->join('assignments', function ($join) {
                $join->on('assignments.judge_id', '=', 'users.id');
            });

        return $this->searchByName($query, $name)
            ->take($limit)
            ->get();
    }

    /**
     * Assigns a user to a given role.
     *
     * @param  object  $user
     * @param  int  $roleId
     * @return mixed|void
     */
    public function assignToRole($user, $roleId)
    {
        $roleIds = $user->roles->just('id');

        if (! in_array($roleId, $roleIds)) {
            $user->roles()->attach($roleId);
        }
    }

    /**
     * Unassign a role from a user.
     *
     * @param  object  $user
     * @param  int  $roleId
     * @return mixed|void
     */
    public function unassignRole($user, $roleId)
    {
        $user->roles()->detach($roleId);
    }

    /**
     * Sets the preferred language for the user.
     *
     * @param  int  $accountId
     * @param  string  $language
     * @return mixed
     */
    public function setPreferredLanguage(User $user, $accountId, $language)
    {
        $user->accounts()->updateExistingPivot($accountId, ['language' => $language]);
    }

    /**
     * Get the preferred language for the required user and account.
     *
     * @param  int  $accountId
     * @return mixed
     */
    public function getPreferredLanguage(User $user, $accountId)
    {
        $membership = $user->memberships->where('account_id', $accountId)->first();

        return $membership ? $membership->language : null;
    }

    /**
     * Get the current language for the required user.
     *
     * @return mixed
     */
    public function getCurrentLanguage(User $user)
    {
        return $this->getPreferredLanguage($user, current_account_id());
    }

    /**
     * Counts the accounts that the user is a member of.
     *
     * @return int
     */
    public function countAccounts(User $user)
    {
        return count($user->globalUser->memberships ?? []);
    }

    /**
     * Return a collection of users that have been assigned the allowed roles, and
     * have not been assigned any of the denied roles.
     *
     * @return mixed
     */
    public function getWithinRoles(array $allowedRoleIds, array $deniedRoleIds)
    {
        return $this->getQuery()->inAccount(current_account(), true)
            ->select(['users.*', 'memberships.deleted_at'])
            ->join('role_user', 'role_user.user_id', '=', 'users.id')
            ->whereIn('role_user.role_id', $allowedRoleIds)
            ->whereNotIn('role_user.role_id', $deniedRoleIds)
            ->whereNotNull('users.global_id')
            ->distinct()
            ->get();
    }

    /**
     * Returns all of the active users for the provided account, or the current account if one isn't provided.
     *
     * @param  int  $accountId
     * @return \Illuminate\Support\Collection
     */
    public function getAllForAccount($accountId = null)
    {
        return $this->getQuery()
            ->whereHas('memberships', function ($query) use ($accountId) {
                $query->whereAccountId($accountId ?: current_account_id());
            })
            ->get();
    }

    /**
     * Returns all of the trashed users for the provided account, or the current account if one isn't provided.
     *
     * @param  int  $accountId
     * @return \Illuminate\Support\Collection
     */
    public function getTrashedForAccount($accountId = null)
    {
        return $this->getQuery()
            ->select('users.*')
            ->join('memberships', 'memberships.user_id', '=', 'users.id')
            ->where('memberships.account_id', '=', $accountId ?: current_account_id())
            ->whereNotNull('memberships.deleted_at')
            ->get();
    }

    /**
     * Returns the user, if they are a member of the current account.
     *
     * @param  int  $userId
     * @param  bool  $withTrashed
     * @param  int  $accountId
     * @return User
     */
    public function getInAccount($userId, $withTrashed = false, $accountId = null)
    {
        $query = $this->getQuery()
            ->select('users.*')
            ->join('memberships', 'memberships.user_id', '=', 'users.id')
            ->where('memberships.account_id', '=', $accountId ?: current_account_id())
            ->where('users.id', $userId);

        if (! $withTrashed) {
            $query->whereNull('memberships.deleted_at');
        }

        return $query->first();
    }

    /**
     * Returns an array of UserIds for the specified account.
     *
     * @param  int  $accountId
     * @param  array  $limitTo Restrict the query to only the ids provided.
     * @return array
     */
    public function getIdsForAccount($accountId = null, array $limitTo = [])
    {
        $query = DB::table('memberships')
            ->where('account_id', '=', $accountId ?: current_account_id())
            ->whereNull('deleted_at');

        if ($limitTo) {
            $query->whereIn('user_id', $limitTo);
        }

        return $query->pluck('user_id')->all();
    }

    /**
     * Returns a collection of models given an array of IDs.
     *
     * @param  bool  $order
     * @return \Illuminate\Support\Collection
     */
    public function getByIds(array $ids, $order = false)
    {
        $query = $this->getQuery()->whereIn('id', $ids);

        if ($order) {
            $query->orderBy('first_name', 'asc')
                ->orderBy('last_name', 'asc');
        }

        return $query->get();
    }

    /**
     * Retrieves the user with the given auth_token, optionally clearing out the token so it cannot be reused.
     *
     * @param  string  $token
     * @param  bool  $clear
     * @return User
     */
    public function requireByAuthToken($token, $clear = true)
    {
        $user = $this->requireBy('auth_token', $token);

        if ($clear) {
            $user->authToken = null;
            $this->save($user);
        }

        return $user;
    }

    /**
     * Should return a user object based on the email address.
     *
     * @param  string  $email
     * @param  string  $mobile
     * @return object|null
     */
    public function getByEmailOrMobile($email, $mobile)
    {
        $query = $this->getQuery();

        if ($email) {
            $query->orWhere('email', $email);
        }

        if ($mobile) {
            $query->orWhere('mobile', $mobile);
        }

        $users = $query->get();

        if ($email && $users->count() >= 2) {
            $users = $users->where('email', $email);
        }

        return $users->first();
    }

    /**
     * Retrieve the total number of new users for the given time period.
     *
     * @param  int  $amount
     * @param  string  $period Eg. days, hours, weeks, months
     * @param  int  $offset Number of period to offset by. For example: 24 if period is set to hours.
     * @return mixed
     */
    public function getNewUserTotal($amount, $period, $offset = 0)
    {
        $method = 'sub'.ucfirst($period);
        $start = new Carbon;
        $end = new Carbon;

        $start->$method($amount + $offset);

        if ($offset) {
            $end->$method($offset);
        }

        $query = $this->getQuery()->select(DB::raw('COUNT(users.id) AS total'));

        $query->join('memberships', function ($join) use ($start, $end) {
            $join->on('memberships.user_id', '=', 'users.id');
            $join->whereNull('memberships.deleted_at');
            $join->where('memberships.account_id', '=', current_account_id());
            $join->where('memberships.created_at', '>=', $start);
            $join->where('memberships.created_at', '<=', $end);
        });

        return $query->first()->total;
    }

    /**
     * Retrieve the total amount of unconfirmed users at a point in time.
     *
     * @param  int  $amount The number of units back in time
     * @param  string  $unit Eg. hours, days, weeks, months
     * @return mixed
     */
    public function getUnconfirmedTotal($amount = 0, $unit = null)
    {
        $time = new Carbon;

        if ($unit) {
            $method = 'sub'.ucfirst($unit);
            $time->$method($amount);
        }

        $query = $this->getQuery()->select(DB::raw('COUNT(users.id) AS total'))
            ->inAccount(current_account())
            ->where('users.created_at', '<=', $time);

        $query->where(function ($query) use ($time) {
            $query->whereNull('users.confirmed_at');
            $query->orWhere('users.confirmed_at', '>=', $time);
        });

        return $query->first()->total;
    }

    /**
     * Retrieve the total amount of users with at least one entry at a point in time.
     *
     * @param  int  $seasonId
     * @param  int  $amount The number of units back in time
     * @param  string  $unit Eg. hours, days, weeks, months
     * @return mixed
     */
    public function getWithEntriesTotal($seasonId, $amount = 0, $unit = null)
    {
        $time = new Carbon;

        if ($unit) {
            $method = 'sub'.ucfirst($unit);
            $time->$method($amount);
        }

        $query = $this->getQuery()->select(DB::raw('COUNT(DISTINCT users.id) AS total'))
            ->inAccount(current_account())
            ->join('entries', 'entries.user_id', '=', 'users.id')
            ->where('entries.account_id', '=', current_account_id())
            ->whereNull('entries.deleted_at')
            ->where('entries.created_at', '<=', $time);

        if ($seasonId) {
            $query->where('entries.season_id', '=', $seasonId);
        }

        return $query->first()->total;
    }

    /**
     * Retrieve the total amount of entrants with no entries at a point in time.
     *
     * @param  int  $seasonId
     * @param  int  $amount The number of units back in time
     * @param  string  $unit Eg. hours, days, weeks, months
     * @return mixed
     */
    public function getWithoutEntriesTotal($seasonId, $amount = 0, $unit = null)
    {
        $time = new Carbon;

        if ($unit) {
            $method = 'sub'.ucfirst($unit);
            $time->$method($amount);
        }

        $query = $this->getQuery()->select(DB::raw('COUNT(DISTINCT users.id) AS total'))
            ->inAccount(current_account())
            ->where('users.created_at', '<=', $time)
            ->join('role_user', 'users.id', '=', 'role_user.user_id')
            ->join('permissions', 'role_user.role_id', '=', 'permissions.role_id')
            ->where('permissions.resource', '=', 'EntriesOwner')
            ->where('permissions.action', '=', 'create')
            ->where('permissions.mode', '=', 'allow');

        return $query->first()->total - $this->getWithEntriesTotal($seasonId, $amount, $unit);
    }

    /**
     * Return all users for the export use-case, with the provided user ids.
     *
     * @return Collection
     */
    public function getForExport(array $userIds)
    {
        $seasonId = current_account()->activeSeason()->id;

        $subQuery = "SELECT MIN(created_at) FROM event_logs
          WHERE event_logs.user_id = users.id
          AND event_logs.season_id = $seasonId
          AND resource = 'auth'
          AND action = 'logged-in'";

        return $this->getQuery()->with('roles')
            ->select('users.*', DB::raw("($subQuery) AS first_login"))
            ->whereIN('users.id', $userIds)
            ->get();
    }

    /**
     * @return void
     */
    public function updateCookiePreferences(User $user, array $cookies)
    {
        $this->getMembershipQuery(current_account(), true)
            ->where('memberships.user_id', $user->id)
            ->update(['memberships.cookies' => json_encode($cookies)]);
    }

    /**
     * @param  string  $issuer
     * @param  string  $nameId
     * @return mixed
     */
    public function getSamlUser($issuer, $nameId)
    {
        return $this->getMembershipQuery(current_account())
            ->select('users.*')
            ->where('memberships.saml_issuer', $issuer)
            ->where('memberships.saml_name_id', $nameId)
            ->first();
    }

    /**
     * @param  string  $issuer
     * @param  int  $userId
     * @return mixed
     */
    public function getSamlUserById($issuer, $userId)
    {
        return $this->getMembershipQuery(current_account())
            ->select('users.*')
            ->where('memberships.saml_issuer', $issuer)
            ->where('memberships.user_id', $userId)
            ->first();
    }

    /**
     * @param  string  $issuer
     * @param  string  $nameId
     * @return void
     */
    public function updateSamlUser(User $user, $issuer, $nameId)
    {
        $this->getMembershipQuery(current_account())
            ->where('memberships.user_id', $user->id)
            ->update([
                'memberships.saml_issuer' => $issuer,
                'memberships.saml_name_id' => $nameId,
            ]);
    }

    /**
     * Destroy all non-guest user records that have no associated memberships.
     *
     * @return mixed
     */
    public function destroyOrphaned(bool $dry = false)
    {
        return $this->getQuery()
            ->leftJoin('memberships', 'memberships.user_id', '=', 'users.id')
            ->leftJoin('entries', function ($join) {
                $join->on('entries.user_id', '=', 'users.id')
                    ->whereNull('entries.deleted_at');
            })
            ->leftJoin('comments', function ($join) {
                $join->on('comments.user_id', '=', 'users.id')
                    ->whereNull('comments.deleted_at');
            })
            ->leftJoinSub(
                $this->orphanedByPanelSubQuery(),
                'filtered_panels',
                fn($join) => $join->on('users.id', '=', 'filtered_panels.user_id')
            )
            ->whereNull('memberships.id')
            ->where(function ($query) {
                $query->where('created_by', '!=', 'guest')
                    ->orWhereNull('created_by');
            })
            ->whereNull('entries.id')
            ->whereNull('comments.id')
            ->whereNull('filtered_panels.user_id')
            ->when($dry,
                fn($query) => $query->select('users.id')->distinct()->count(),
                fn($query) => $query->delete()
            );
    }

    /**
     * @return Collection
     */
    public function getWithoutSlug()
    {
        return $this->getQuery()
            ->where('slug', '=', '')
            ->orWhereNull('slug')
            ->get();
    }

    /**
     * Wipe out globalId for permanently deleted users
     *
     * @return mixed
     */
    public function fixDestroyed()
    {
        return $this->getQuery()
            ->whereNull('email')
            ->whereNull('mobile')
            ->whereNotNull('global_id')
            ->update(['global_id' => null]);
    }

    public function getByGlobalId($globalId)
    {
        return $this->getBy('global_id', $globalId)->first();
    }

    /**
     * Retrieve a user based on the mobile.
     *
     * @param  string  $mobile
     * @return User
     */
    public function getByMobile($mobile)
    {
        return $this->getBy('mobile', $mobile)->first();
    }

    public function refreshUser(User $user): User
    {
        return $user->refresh();
    }

    /**
     * Get all users with chapter limited role(s).
     */
    public function getChapterManagers()
    {
        $this->joinRoles()->query()
            ->inAccount(current_account(), true)
            ->select('users.*')
            ->where('roles.chapter_limited', '=', true)
            ->whereNotNull('users.global_id')
            ->distinct();

        return $this->get();
    }

    public function joinRoles(bool $withTrashed = false): self
    {
        $this->query()
            ->join('role_user', 'role_user.user_id', '=', 'users.id')
            ->join('roles', function (JoinClause $join) use ($withTrashed) {
                $join->on('roles.id', '=', 'role_user.role_id')
                    ->where('roles.account_id', $this->currentAccountId())
                    ->unless($withTrashed, fn(JoinClause $join) => $join->whereNull('roles.deleted_at'));
            });

        return $this;
    }

    public function joinPermissions(bool $withTrashed = false): self
    {
        $this->joinRoles($withTrashed)->query()
            ->join('permissions', 'permissions.role_id', '=', 'roles.id');

        return $this;
    }

    public function allowedWith(Permission ...$permissions): self
    {
        $query = $this->query()->distinct('users.id');

        foreach ($permissions as $permission) {
            $query->whereExists(function ($query) use ($permission) {
                $query->select(DB::raw(1))
                    ->from('permissions')
                    ->whereRaw('permissions.role_id = roles.id')
                    ->where('permissions.resource', $permission->resource)
                    ->where('permissions.action', $permission->action)
                    ->where('permissions.mode', $permission->mode);
            });
        }

        return $this;
    }

    private function orphanedByPanelSubQuery(): callable
    {
        return fn($query) => $query->select('panel_judges.user_id', 'panels.id as panel_id')
            ->from('panel_judges')
            ->join('panels', fn($join) => $join->on('panel_judges.panel_id', '=', 'panels.id')->whereNull('panels.deleted_at'))
            ->leftJoin('assignment_panel', 'panels.id', '=', 'assignment_panel.panel_id')
            ->leftJoin('assignments', 'assignment_panel.assignment_id', '=', 'assignments.id')
            ->whereNotNull('assignment_panel.panel_id')
            ->whereNotNull('assignments.id');
    }
}
