<?php

namespace AwardForce\Modules\Entries\Models;

use AwardForce\Library\Database\Eloquent\HasLocalId;
use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Encrypter\Traits\HasEncrypter;
use AwardForce\Modules\Accounts\Traits\BelongsToAccount;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Awards\Data\Award;
use AwardForce\Modules\Awards\Events\AwardsCleared;
use AwardForce\Modules\Awards\Services\Badgeable;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Documents\Contracts\HasDocuments as HasDocumentsContract;
use AwardForce\Modules\Documents\Contracts\HasMergeFields;
use AwardForce\Modules\Documents\Models\HasDocuments;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\EntryItem;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Ecommerce\Orders\Data\OrderItem;
use AwardForce\Modules\Entries\Events\CategoryWasChanged;
use AwardForce\Modules\Entries\Events\ChapterWasChanged;
use AwardForce\Modules\Entries\Events\EntryHealthWasCleared;
use AwardForce\Modules\Entries\Events\EntryHealthWasFlagged;
use AwardForce\Modules\Entries\Events\EntryOwnerChanged;
use AwardForce\Modules\Entries\Events\EntryResubmissionWasRequired;
use AwardForce\Modules\Entries\Events\EntryWasArchived;
use AwardForce\Modules\Entries\Events\EntryWasCopied;
use AwardForce\Modules\Entries\Events\EntryWasCreated;
use AwardForce\Modules\Entries\Events\EntryWasDeleted;
use AwardForce\Modules\Entries\Events\EntryWasEligible;
use AwardForce\Modules\Entries\Events\EntryWasIneligible;
use AwardForce\Modules\Entries\Events\EntryWasModerated;
use AwardForce\Modules\Entries\Events\EntryWasRestored;
use AwardForce\Modules\Entries\Events\EntryWasResubmitted;
use AwardForce\Modules\Entries\Events\EntryWasRevertedToInProgress;
use AwardForce\Modules\Entries\Events\EntryWasSubmitted;
use AwardForce\Modules\Entries\Events\EntryWasUnarchived;
use AwardForce\Modules\Entries\Events\EntryWasUpdated;
use AwardForce\Modules\Entries\Events\TitleWasChanged;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use AwardForce\Modules\Forms\Collaboration\Contracts\Collaborative;
use AwardForce\Modules\Forms\Collaboration\Traits\HasCollaborators;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\Fieldable;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasFields;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasFlatValues;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasScores;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ScoresProvider;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ValuesProvider;
use AwardForce\Modules\Forms\Formables\Boundary\Formable;
use AwardForce\Modules\Forms\Formables\ResourceId;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Database\Relations\BelongsToForm;
use AwardForce\Modules\Forms\Forms\Database\Relations\HasSubmission;
use AwardForce\Modules\Forms\Forms\Enums\Resource;
use AwardForce\Modules\Forms\Forms\FormId;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Services\FundAllocationsTable;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Grants\Events\EntryLockScoringWasChanged;
use AwardForce\Modules\Grants\Events\GrantStatusWasAdded;
use AwardForce\Modules\Grants\Events\GrantStatusWasChanged;
use AwardForce\Modules\Grants\Events\GrantStatusWasUpdated;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Grants\Repositories\GrantStatusRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Integrations\Plagiarism\Data\PlagiarismScan;
use AwardForce\Modules\Integrations\Plagiarism\PlagiarismDetection;
use AwardForce\Modules\Integrations\Plagiarism\Values\EntryScanStatus;
use AwardForce\Modules\Integrations\Plagiarism\Values\PlagiarismScanStatus;
use AwardForce\Modules\ReviewFlow\Data\ReviewStageAction;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\Seasons\Traits\Seasonal;
use AwardForce\Modules\Stars\Models\HasStars;
use AwardForce\Modules\Stars\Models\Starrable;
use AwardForce\Modules\Tags\Contracts\TaggableModel;
use AwardForce\Modules\Tags\Events\TagWasAdded;
use AwardForce\Modules\Tags\Models\Tag;
use AwardForce\Modules\Tags\Traits\Taggable;
use Carbon\Carbon;
use Eloquence\Behaviours\HasSlugs;
use Eloquence\Behaviours\Slug;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Platform\Database\Eloquent\Archivisation;
use Platform\Database\Eloquent\TranslatableModel;
use Platform\Search\HasValues;
use Tectonic\Localisation\Contracts\Translatable;
use Tectonic\Localisation\Translator\Translations;

/**
 * AwardForce\Modules\Entries\Models\Entry
 *
 * @property int $id
 * @property int $accountId
 * @property int $formId
 * @property int $userId
 * @property int $seasonId
 * @property int $chapterId
 * @property int $categoryId
 * @property int|null $orderId
 * @property \Eloquence\Behaviours\Slug|null $slug
 * @property int|null $localId
 * @property string $title
 * @property string $moderationStatus
 * @property string $healthStatus
 * @property ReviewStageAction $reviewStatusAction
 * @property EntryScanStatus $plagiarismScanStatus
 * @property int|null $division
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property \Illuminate\Support\Carbon|null $submittedAt
 * @property string|null $resubmissionRequiredAt
 * @property \Illuminate\Support\Carbon|null $resubmittedAt
 * @property string|null $ineligibleAt
 * @property string|null $eligibleAt
 * @property \Illuminate\Support\Carbon|null $deletedAt
 * @property \Illuminate\Support\Carbon|null $archivedAt
 * @property string|null $wipedAt
 * @property array|null $values
 * @property array|null $hashes
 * @property array|null $protected
 * @property int|null $grantStatusId
 * @property string|null $grantEndsAt
 * @property string|null $scores
 * @property float|null $totalScore
 * @property array|null $eligibleTabs
 * @property int|null $invitedChapterId
 * @property int|null $invitedCategoryId
 * @property string|null $invitedAt
 * @property \Illuminate\Support\Carbon|null $deadlineAt
 * @property string|null $deadlineTimezone
 * @property int|null $filesCount
 * @property-read \AwardForce\Modules\Accounts\Models\Account|null $account
 * @property-read \AwardForce\Modules\Funding\Data\Allocations<int, Allocation> $allocations
 * @property-read int|null $allocationsCount
 * @property-read \AwardForce\Modules\Assignments\Models\AssignmentCollection<int, Assignment> $assignments
 * @property-read int|null $assignmentsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Award> $awards
 * @property-read int|null $awardsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Award> $badges
 * @property-read int|null $badgesCount
 * @property-read Category|null $category
 * @property-read \Platform\Database\Eloquent\Collection<int, Award> $certificates
 * @property-read int|null $certificatesCount
 * @property-read Chapter|null $chapter
 * @property-read \AwardForce\Modules\Entries\Models\ContractCollection<int, \AwardForce\Modules\Entries\Models\Contract> $contracts
 * @property-read int|null $contractsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, \AwardForce\Modules\Documents\Models\Document> $documents
 * @property-read int|null $documentsCount
 * @property-read Duplicate|null $duplicate
 * @property-read User|null $entrant
 * @property-read Form|null $form
 * @property-read \Carbon|null $deadline
 * @property array $fieldValues
 * @property \AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields $fields
 * @property bool $star
 * @property-read \Platform\Database\Eloquent\Collection<int, GrantReport> $grantReports
 * @property-read int|null $grantReportsCount
 * @property-read GrantStatus|null $grantStatus
 * @property-read Order|null $order
 * @property-read \Platform\Database\Eloquent\Collection<int, OrderItem> $orderItems
 * @property-read int|null $orderItemsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Order> $orders
 * @property-read int|null $ordersCount
 * @property-read \AwardForce\Modules\Integrations\Plagiarism\Data\PlagiarismScanCollection<int, PlagiarismScan> $plagiarismScans
 * @property-read int|null $plagiarismScansCount
 * @property-read \Platform\Database\Eloquent\Collection<int, ReviewTask> $reviewTasks
 * @property-read int|null $reviewTasksCount
 * @property-read \AwardForce\Modules\Seasons\Models\Season|null $season
 * @property-read \Platform\Database\Eloquent\Collection<int, \AwardForce\Modules\Stars\Models\Star> $stars
 * @property-read int|null $starsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Tag> $tags
 * @property-read int|null $tagsCount
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Tectonic\LaravelLocalisation\Database\Translation> $translations
 * @property-read int|null $translationsCount
 * @property-read User|null $user
 *
 * @method static \Platform\Database\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Entry archived()
 * @method static \Platform\Database\Eloquent\Builder|Entry current()
 * @method static \Platform\Database\Eloquent\Builder|Entry forSeason($seasonId)
 * @method static \Platform\Database\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Entry joinTaggables()
 * @method static \Platform\Database\Eloquent\Builder|Entry moderationApproved()
 * @method static \Platform\Database\Eloquent\Builder|Entry newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|Entry newQuery()
 * @method static \Platform\Database\Eloquent\Builder|Entry notRejected()
 * @method static Builder|Entry onlyTrashed()
 * @method static \Platform\Database\Eloquent\Builder|Entry preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|Entry query()
 * @method static \Platform\Database\Eloquent\Builder|Entry submitted()
 * @method static \Platform\Database\Eloquent\Builder|Entry whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereArchivedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereCategoryId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereChapterId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereDeadlineAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereDeadlineTimezone($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereDeletedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereDivision($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereEligibleAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereEligibleTabs($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereFilesCount($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereFormId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereGrantEndsAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereGrantStatusId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereHashes($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereHealthStatus($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereIneligibleAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereInvitedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereInvitedCategoryId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereInvitedChapterId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereLocalId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereModerationStatus($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereOrderId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry wherePlagiarismScanStatus($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereProtected($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereResubmissionRequiredAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereResubmittedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereReviewStatusAction($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereScores($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereSlug($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereSubmittedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereTitle($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereTotalScore($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereUpdatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereUserId($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereValues($value)
 * @method static \Platform\Database\Eloquent\Builder|Entry whereWipedAt($value)
 * @method static Builder|Entry withTrashed()
 * @method static Builder|Entry withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Entry extends Model implements Badgeable, Collaborative, Formable, HasDocumentsContract, HasFields, HasMergeFields, HasValues, ScoresProvider, Starrable, Submittable, TaggableModel, Translatable, ValuesProvider
{
    use Archivisation;
    use BelongsToAccount;
    use BelongsToForm;
    use Fieldable;
    use FundAllocationsTable;
    use HasCollaborators;
    use HasDocuments;
    use HasEncrypter;
    use HasFlatValues;
    use HasLocalId;
    use HasScores;
    use HasSlugs;
    use HasStars;
    use HasSubmission;
    use Seasonal;
    use SoftDeletes;
    use Taggable;
    use TranslatableModel;
    use Translations;

    /**
     * @var string
     */
    const MODERATION_STATUS_APPROVED = 'approved';

    const MODERATION_STATUS_UNDECIDED = 'undecided';

    const MODERATION_STATUS_REJECTED = 'rejected';

    /**
     * @var string
     */
    const PAYMENT_STATUS_PAID = 'paid';

    const PAYMENT_STATUS_AWAITING_PAYMENT = 'awaiting_payment';

    /**
     * @var string
     */
    const HEALTH_STATUS_OK = 'ok';

    const HEALTH_STATUS_ISSUE = 'issue';

    /**
     * @var array
     */
    public static $moderationStatuses = ['approved', 'undecided', 'rejected'];

    /**
     * @var array
     */
    public static $paymentStatuses = ['paid', 'awaiting_payment'];

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'entries';

    protected $casts = [
        'values' => 'array',
        'hashes' => 'array',
        'protected' => 'array',
        'eligible_tabs' => 'array',
        'deadline_at' => 'datetime',
        'slug' => Slug::class,
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['title'];

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * The following variables set defaults for when a model object is
     * copied or deleted, and these are easily overloaded for more custom objects.
     *
     * @var string
     */
    public $copiedEvent = EntryWasCopied::class;

    public $deletedEvent = EntryWasDeleted::class;
    public $undeletedEvent = EntryWasRestored::class;
    public $archivedEvent = EntryWasArchived::class;
    public $unarchivedEvent = EntryWasUnarchived::class;
    public $tagAddedEvent = TagWasAdded::class;

    /** @var string */
    private $cachedPaymentStatus;

    public function entrant()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return HasMany
     */
    public function allocations()
    {
        return $this->hasMany(Allocation::class)
            ->whereHas('fund');
    }

    /**
     * Entries have a number of date time values that need to be managed.
     *
     * @return array
     */
    public function getDates()
    {
        return ['created_at', 'updated_at', 'submitted_at', 'deleted_at', 'archived_at', 'resubmitted_at', 'invitedAt', 'deadlineAt'];
    }

    public function awards()
    {
        return $this->belongsToMany(Award::class);
    }

    public function badges()
    {
        return $this->belongsToMany(Award::class)
            ->where('awards.type', 'badge')
            ->with('tags')
            ->withPivot('score_set_id');
    }

    public function certificates()
    {
        return $this->belongsToMany(Award::class)->where('awards.type', 'certificate')->distinct();
    }

    public function reviewTasks()
    {
        return $this->hasMany(ReviewTask::class)
            ->whereHas('reviewStage', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->whereNotNull('action_taken');
    }

    public function hasBadge(?int $scoreSetId = null): bool
    {
        return (bool) $this->badges->filter($this->badgeFilter($scoreSetId))->count();
    }

    public function firstBadge(?int $scoreSetId = null)
    {
        return $this->badges->filter($this->badgeFilter($scoreSetId))->first();
    }

    public function badgesForScoreSet(?int $scoreSetId = null)
    {
        return $this->badges->filter($this->badgeFilter($scoreSetId))->unique('id');
    }

    public function badgesForScoreSets(array $scoreSetIds)
    {
        return $this->badges->filter($this->badgeFilter($scoreSetIds))->unique('id');
    }

    public function hasBadgeForScoreSet(Award $badge, ?int $scoreSetId = null): bool
    {
        return $this->badges
            ->filter(fn(Award $b) => $b->id === $badge->id)
            ->filter($this->badgeFilter($scoreSetId))
            ->isNotEmpty();
    }

    public function entrantBadges()
    {
        return $this->badges->filter(fn(Award $badge) => $badge->inEntrantView)->unique('id');
    }

    /**
     * Simply provides a closure that can be used for some repeatable operations.
     *
     * @return \Closure
     */
    private function badgeFilter($scoreSetIds)
    {
        if (! is_array($scoreSetIds)) {
            $scoreSetIds = [$scoreSetIds];
        }

        return function (Award $badge) use ($scoreSetIds) {
            $filterByTags = ! empty(array_intersect($badge->tags->just('id'), $this->tags->just('id')));
            $filterByAllowsAnyScoreSet = ($badge->pivot->score_set_id === null && $badge->score_set_option === 'all');

            return $filterByTags && ($filterByAllowsAnyScoreSet || in_array($badge->pivot->score_set_id, $scoreSetIds, true));
        };
    }

    public function hasCertificate(): bool
    {
        return (bool) count($this->certificates);
    }

    public function clearAwards()
    {
        $this->awards()->sync([]);
        $this->raise(new AwardsCleared($this));
    }

    public function addAward(Award $award, ?int $scoreSetId = null)
    {
        if (! $this->canAddAward($award, $scoreSetId)) {
            return;
        }

        $this->awards()->attach($award->id, ['score_set_id' => $scoreSetId]);
        $this->refresh();
    }

    public function setTitleAttribute(string $title)
    {
        if ($title != $this->title) {
            $this->attributes['title'] = $title;
            $this->raise(new TitleWasChanged($this, $title));
        }
    }

    public function setCategoryIdAttribute($categoryId)
    {
        if ($categoryId != $this->categoryId) {
            $this->attributes['category_id'] = $categoryId;
            $this->raise(new CategoryWasChanged($this));
        }
    }

    public function setChapterIdAttribute($chapterId)
    {
        if ($chapterId != $this->chapterId) {
            $this->attributes['chapter_id'] = $chapterId;
            $this->raise(new ChapterWasChanged($this));
        }
    }

    /**
     * @return array
     */
    public function getTranslatableFields()
    {
        return [];
    }

    /**
     * @param  mixed  $action
     * @return ReviewStageAction
     */
    public function getReviewStatusActionAttribute($action)
    {
        return $action ? new ReviewStageAction($action) : null;
    }

    public function setReviewStatusActionAttribute(?ReviewStageAction $action = null)
    {
        $this->attributes['review_status_action'] = $action ? $action->toString() : null;
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Entry belongs to a category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo(Category::class)->withTrashed();
    }

    /**
     * Get all of the tags for the entry.
     */
    public function tags()
    {
        return $this->morphToMany(Tag::class, 'taggable');
    }

    /**
     * Entry belongs to a chapter.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function chapter()
    {
        return $this->belongsTo(Chapter::class)->withTrashed();
    }

    /**
     * Creates a new entry in the current account.
     *
     * @return Entry
     */
    private static function create($userId, $title, $seasonId, $formId, $chapterId, $categoryId)
    {
        $entry = new self;

        $entry->accountId = current_account_id();
        $entry->title = $title;
        $entry->userId = $userId;
        $entry->seasonId = $seasonId;
        $entry->formId = $formId;
        $entry->chapterId = $chapterId;
        $entry->categoryId = $categoryId;

        return $entry;
    }

    public static function start($userId, $title, $seasonId, $formId, $chapterId, $categoryId)
    {
        $entry = self::create($userId, $title, $seasonId, $formId, $chapterId, $categoryId);

        $entry->raise(new EntryWasCreated($entry));

        return $entry;
    }

    public static function startForCopy($userId, $title, $seasonId, $formId, $chapterId, $categoryId, $sourceId)
    {
        $entry = self::create($userId, $title, $seasonId, $formId, $chapterId, $categoryId);
        $entry->save();

        $entry->raise(new EntryWasCopied($entry, $sourceId));

        return $entry;
    }

    /**
     * Update the entry with the provided information.
     *
     * @param  int  $userId
     * @param  int  $chapterId
     * @param  int  $categoryId
     * @param  string  $title
     * @return $this
     */
    public function edit($userId, $chapterId, $categoryId, $title)
    {
        $this->userId = $userId;
        $this->chapterId = $chapterId;
        $this->categoryId = $categoryId;
        $this->title = $title;

        if ($this->isDirty('user_id')) {
            $this->raise(new EntryOwnerChanged($this));
        }

        if ($this->isDirty()) {
            $this->raise(new EntryWasUpdated($this));
        }

        return $this;
    }

    public function editTitle(string $title)
    {
        $this->title = $title;

        if ($this->isDirty()) {
            $this->raise(new EntryWasUpdated($this));
        }

        return $this;
    }

    public function editCategory(int $categoryId)
    {
        $this->categoryId = $categoryId;

        if ($this->isDirty()) {
            $this->raise(new EntryWasUpdated($this));
        }

        return $this;
    }

    public function editChapter(int $chapterId)
    {
        $this->chapterId = $chapterId;

        if ($this->isDirty()) {
            $this->raise(new EntryWasUpdated($this));
        }

        return $this;
    }

    /**
     * Submit an entry, by setting its submittedAt field to the current datetime.
     *
     * @return $this
     */
    public function submit(): Entry
    {
        $this->submittedAt = new \DateTime;
        $this->raise(new EntryWasSubmitted($this));

        return $this;
    }

    /**
     * Resubmit an entry, by setting its resubmittedAt field to the current datetime.
     *
     * @return $this
     */
    public function resubmit()
    {
        $this->resubmittedAt = new \DateTime;
        $this->raise(new EntryWasResubmitted($this));

        return $this;
    }

    /**
     * Moderate the entry by setting the provided moderation status.
     *
     * @param  string  $moderationStatus
     * @return $this
     */
    public function moderate($moderationStatus)
    {
        if ($this->moderationStatus == $moderationStatus) {
            return $this;
        }

        $this->moderationStatus = $moderationStatus;

        $this->raise(new EntryWasModerated($this));

        return $this;
    }

    public function setGrantStatus(?int $grantStatusId)
    {
        return $this->grantStatusId = $grantStatusId;
    }

    public function setGrantEndDate(?string $grantEndDate)
    {
        return $this->grantEndsAt = $grantEndDate;
    }

    /**
     * Returns true if the entry is Approved.
     *
     * @return bool
     */
    public function isApproved()
    {
        return $this->moderationStatus == self::MODERATION_STATUS_APPROVED;
    }

    /**
     * Returns true if the entry is Undecided.
     *
     * @return bool
     */
    public function isUndecided()
    {
        return $this->moderationStatus == self::MODERATION_STATUS_UNDECIDED;
    }

    /**
     * Returns true if the entry is Rejected.
     *
     * @return bool
     */
    public function isRejected()
    {
        return $this->moderationStatus == self::MODERATION_STATUS_REJECTED;
    }

    /**
     * Query scope for moderation approved entries only.
     *
     * @return Builder
     */
    public function scopeModerationApproved(Builder $query)
    {
        return $query->where('moderation_status', self::MODERATION_STATUS_APPROVED);
    }

    /**
     * Query scope for ignoring entries that have been rejected by moderation.
     *
     * @return Builder
     */
    public function scopeNotRejected(Builder $query)
    {
        return $query->where('moderation_status', '!=', self::MODERATION_STATUS_REJECTED);
    }

    /**
     * Query scope for submitted entries only.
     *
     * @return Builder
     */
    public function scopeSubmitted(Builder $query)
    {
        return $query->whereNotNull('submitted_at');
    }

    /**
     * @return HasMany
     */
    public function assignments()
    {
        return $this->hasMany(Assignment::class);
    }

    /**
     * @return HasMany
     */
    public function contracts()
    {
        return $this->hasMany(Contract::class);
    }

    /**
     * Entry attachments with field values.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function loadAttachments()
    {
        return $this->attachments()->get();
    }

    /**
     * Entry belongs to an order.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function orders()
    {
        return $this->belongsToMany(Order::class, 'order_items');
    }

    /**
     * @return HasMany
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class)->has('order');
    }

    public function currentOrderItems()
    {
        return $this->orderItems()->whereHas(
            'order',
            function (Builder $query) {
                $query->whereNull('deleted_at');
            }
        );
    }

    public function paymentInProgress(): bool
    {
        if ($this->cachedPaymentStatus) {
            return $this->cachedPaymentStatus == self::PAYMENT_STATUS_AWAITING_PAYMENT;
        }

        if (setting()->paidEntries() && ($this->hasOrderAwaitingPayment() || $this->inCart())) {
            $this->cachedPaymentStatus = self::PAYMENT_STATUS_AWAITING_PAYMENT;

            return true;
        }

        return setting()->paidEntries() && ! $this->paymentComplete();
    }

    public function paymentComplete(): bool
    {
        if ($this->cachedPaymentStatus) {
            return $this->cachedPaymentStatus == self::PAYMENT_STATUS_PAID;
        }

        if (setting()->paidEntries() && $this->hasPaidOrder()) {
            $this->cachedPaymentStatus = self::PAYMENT_STATUS_PAID;

            return true;
        }

        return false;
    }

    /**
     * Flag an issue on the entry by updating the health status
     *
     * @param  string  $message
     */
    public function flagIssue($message = '')
    {
        $this->healthStatus = self::HEALTH_STATUS_ISSUE;
        $this->save();

        $this->raise(new EntryHealthWasFlagged($this, $message));
    }

    /**
     * Clear an issue on the entry by updating the health status
     */
    public function clearIssue()
    {
        $this->healthStatus = self::HEALTH_STATUS_OK;
        $this->save();

        $this->raise(new EntryHealthWasCleared($this));
    }

    /**
     * Change status of the entry to "In progress".
     */
    public function revertToInProgress()
    {
        $this->submittedAt = null;
        $this->resubmissionRequiredAt = null;
        $this->resubmittedAt = null;
        $this->ineligibleAt = null;
        $this->eligibleAt = null;
        $this->eligibleTabs = [];
        $this->invitedAt = null;

        $this->raise(new EntryWasRevertedToInProgress($this));
    }

    /**
     * Change status of the entry to "Require resubmission".
     */
    public function requireResubmission()
    {
        if ($this->submitted()) {

            $this->resubmissionRequiredAt = Carbon::now();
            $this->resubmittedAt = null;
        }
        $this->ineligibleAt = null;
        $this->eligibleAt = null;
        $this->eligibleTabs = [];

        $this->raise(new EntryResubmissionWasRequired($this));
    }

    /**
     * Check to see if this entry is health
     *
     * @return bool
     */
    public function isHealthy()
    {
        return $this->healthStatus == self::HEALTH_STATUS_OK;
    }

    /**
     * Check to see if this entry status is/contains "In progress".
     *
     * @return bool
     */
    public function inProgress()
    {
        return ! $this->submittedAt;
    }

    /**
     * Check to see if this entry status is "Resubmission required".
     *
     * @return bool
     */
    public function resubmissionRequired()
    {
        return $this->resubmissionRequiredAt != null && $this->resubmittedAt === null;
    }

    /**
     * Check to see if this entry status is "Resubmitted".
     *
     * @return bool
     */
    public function resubmitted()
    {
        return $this->resubmittedAt !== null;
    }

    /**
     * Check to see if this entry is unhealthy
     *
     * @return bool
     */
    public function isUnhealthy()
    {
        return $this->healthStatus !== self::HEALTH_STATUS_OK;
    }

    public function plagiarismScans()
    {
        return $this->hasMany(PlagiarismScan::class, 'entry_id');
    }

    public function completedPlagiarismScans()
    {
        return $this->plagiarismScans()->whereIn('status', [PlagiarismScanStatus::COMPLETED]);
    }

    public function updatePlagiarismScanStatus(PlagiarismDetection $integration)
    {
        $this->plagiarismScanStatus = EntryScanStatus::fromEntry($this, $integration);
        $this->timestamps = false;

        $this->save();

        $this->timestamps = true;
    }

    /**
     * @return void
     */
    public function setPlagiarismScanStatusAttribute(EntryScanStatus $status)
    {
        $this->attributes['plagiarism_scan_status'] = (string) $status;
    }

    /**
     * @return EntryScanStatus
     */
    public function getPlagiarismScanStatusAttribute()
    {
        if ($plagiarismScanStatus = ($this->attributes['plagiarism_scan_status'] ?? '')) {
            return new EntryScanStatus($plagiarismScanStatus);
        }

        return null;
    }

    public function inCart(): bool
    {
        return (bool) array_first(app(Cart::class)->items(), function ($item) {
            return $item instanceof EntryItem && $this->is($item->entry());
        }, false);
    }

    public function hasOrderAwaitingPayment(): bool
    {
        return $this->orders->contains->awaitingPayment();
    }

    private function hasPaidOrder(): bool
    {
        return $this->orders->contains->paid();
    }

    public function hasActiveCategory(): bool
    {
        return $this->category && ! $this->category->trashed() && $this->category->active;
    }

    public function hasLockedCategory(): bool
    {
        return ($this->submitted() && ! $this->hasActiveCategory()) ||
            ($this->invitedCategoryId && $this->form->invitationOnly) ||
            ($this->category?->locked && ($this->submitted() || $this->resubmissionRequired())) ||
            $this->resubmitted();
    }

    public function addContributor(int $tabId): Contributor
    {
        return $this->contributors()->create(['tab_id' => $tabId]);
    }

    public function addAttachment(int $fileId, int $tabId, ?int $order = null)
    {
        return $this->attachments()->create(array_merge([
            'account_id' => $this->accountId,
            'file_id' => $fileId,
            'tab_id' => $tabId,
        ], $order ? compact('order') : []));
    }

    public function duplicate()
    {
        return $this->hasOne(Duplicate::class);
    }

    public function primary()
    {
        if ($duplicate = $this->duplicate) {
            return $duplicate->primary();
        }
    }

    public function duplicateOf()
    {
        if ($duplicate = $this->duplicate) {
            return $duplicate->duplicateOf();
        }
    }

    public function ownerName(): string
    {
        return $this->entrant->fullName();
    }

    /**
     * @return Builder|\Illuminate\Database\Eloquent\Model|HasMany|\Illuminate\Database\Query\Builder|object
     */
    public function latestReviewTask()
    {
        return $this->reviewTasks()
            ->orderBy('action_at', 'desc')
            ->first();
    }

    public function submissionStatus()
    {
        return match (true) {
            $this->resubmitted() && $this->resubmittedAt > $this->resubmissionRequiredAt => 'resubmitted',
            $this->resubmissionRequired() => 'resubmission_required',
            $this->submitted() => 'submitted',
            $this->isInvited() => 'invited',
            default => 'in_progress',
        };
    }

    protected function excludeTagRelations(): array
    {
        return ['awards', 'certificates', 'badges'];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function grantStatus()
    {
        return $this->belongsTo(GrantStatus::class);
    }

    public function canReceiveFunds()
    {
        return is_null($this->grantStatus) || ! $this->grantStatus->lockFundAllocation;
    }

    public function formableResourceId(): ResourceId
    {
        return new ResourceId($this->id);
    }

    public function formableFormId(): FormId
    {
        return new FormId($this->getAttribute('form_id'));
    }

    public function formableResource(): Resource
    {
        return Resource::Entry;
    }

    public function formType(): string
    {
        return Form::FORM_TYPE_ENTRY;
    }

    public function ineligibility($notificationId = null)
    {
        $this->ineligibleAt = now();
        $this->resubmissionRequiredAt = null;
        $this->raise(new EntryWasIneligible($this, $notificationId));

        return $this;
    }

    public function eligibility($notificationId)
    {
        $this->eligibleAt = now();
        $this->raise(new EntryWasEligible($this, $notificationId));

        return $this;
    }

    public function isIneligible(): bool
    {
        return ! is_null($this->ineligibleAt);
    }

    public function isEligible(): bool
    {
        return ! is_null($this->eligibleAt) && ! $this->isIneligible();
    }

    public function tabIsEligible(Tab $tab): bool
    {
        return in_array((string) $tab->slug, $this->eligibleTabs ?? []);
    }

    public function addEligibleTab(Tab $tab): void
    {
        $this->eligibleTabs = array_unique(array_merge($this->eligibleTabs ?? [], [(string) $tab->slug]));
    }

    public function resetEligibilityStatus(): void
    {
        $this->eligibleTabs = [];
        $this->eligibleAt = null;

        $this->save();
    }

    public function invite(): void
    {
        $this->invitedAt = now();
    }

    public function isInvited(): bool
    {
        return ! is_null($this->invitedAt);
    }

    public function setDeadline($date, $timezone = null)
    {
        $this->attributes['deadline_timezone'] = $date ? ($timezone ?: \Consumer::timezone()) : null;
        $this->attributes['deadline_at'] = date_value($date, $this->attributes['deadline_timezone']);
    }

    public function hasDeadline(): bool
    {
        return ! is_null($this->deadlineAt);
    }

    /**
     * Returns the dealine in set timezone
     *
     * @return \Carbon|null
     */
    public function getDeadlineAttribute()
    {
        if (is_null($this->deadlineAt)) {
            return null;
        }

        return convert_date_to_timezone($this->deadlineAt, $this->deadlineTimezone);
    }

    public function deadlinePassed()
    {
        return $this->deadlineAt && $this->deadlineAt->lt(now());
    }

    protected function getTitleAttribute(?string $value): string
    {
        if (! empty($value)) {
            return $value;
        }

        return $this->isInvited() ? trans('entries.status.invited') : '';
    }

    private function canAddAward(Award $award, ?int $scoreSetId = null): bool
    {
        return match ($award->type) {
            Award::TYPE_CERTIFICATE => ! $this->awards->contains($award->getKey()),
            Award::TYPE_BADGE => ! $this->hasBadgeForScoreSet($award, $scoreSetId),
            default => true
        };
    }

    public function setGrantStatusIdAttribute($value)
    {
        $grantStatusChanged = $this->grantStatusId !== $value;

        $this->attributes['grant_status_id'] = $value;

        if ($grantStatusChanged) {
            $this->raiseGrantStatusEvents($value);
        }
    }

    private function raiseGrantStatusEvents($newGrantStatusId)
    {
        $grantStatuses = app(GrantStatusRepository::class);

        if ((bool) $grantStatuses->getById($this->getOriginal('grant_status_id'))?->lockScoring !== (bool) $grantStatuses->getById($newGrantStatusId)?->lockScoring) {
            $this->raise(new EntryLockScoringWasChanged($this));
        }

        empty($this->getOriginal('grant_status_id'))
            ? $this->raise(new GrantStatusWasAdded)
            : $this->raise(new GrantStatusWasUpdated);

        $this->raise(new GrantStatusWasChanged($this));
        $this->raise(new EntryWasUpdated($this));
    }

    public function categoryShortcode(): ?string
    {
        return translate($this->category)?->shortcode;
    }

    public function isEntry(): bool
    {
        return true;
    }

    public function isGrantReport(): bool
    {
        return false;
    }

    public function displayPackingSlip(): bool
    {
        return feature_enabled('pdfs')
            && $this->category?->packingSlipsEnabled()
            && ! is_null($this->submittedAt);
    }

    public function grantReports(): HasMany
    {
        return $this->hasMany(GrantReport::class);
    }

    public function getMergeFields(): array
    {
        $category = translate($this->category);

        return array_merge($this->entrant?->getMergeFields() ?? [], [
            'entry_name' => htmlspecialchars($this->title),
            'entry_slug' => (string) $this->slug,
            'entry_local_id' => local_id($this),
            'parent_category' => $category?->hasParentCategory() ? htmlspecialchars(translate($category->parent)->name) : '',
            'category' => htmlspecialchars($category?->name),
            'chapter' => htmlspecialchars(translate($this->chapter)?->name),
            'fund_allocations' => $this->fundAllocationsWordTable($this->allocations),
        ]);
    }

    public function setOwner(User $user): self
    {
        $this->userId = $user->id;
        $this->save();

        return $this;
    }

    public function ownedBy(User $user): bool
    {
        return $this->getUserId() === $user->id;
    }

    public function editRoute(): string
    {
        return route('entry-form.entrant.edit', ['entry' => (string) $this->slug]);
    }

    public function verticalKey(): string
    {
        return 'entry';
    }

    public function chapterName(): string
    {
        return (string) $this->chapter?->name;
    }

    public function categoryName(): string
    {
        return (string) $this->category?->name;
    }

    public function hasGrantStatus(): bool
    {
        return ! is_null($this->grantStatusId);
    }

    public function entrantCanCopy(): bool
    {
        return $this->form->settings->enableCopy;
    }

    public function displayId(): bool
    {
        return $this->form->settings->displayId;
    }
}
