<?php

namespace AwardForce\Modules\Ecommerce\Cart\Services\Refreshers;

use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Costing\EntryAmount;
use AwardForce\Modules\Ecommerce\Cart\EntryItem;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Ecommerce\Orders\Data\OrderItem;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Settings\Models\Setting;
use Platform\Features\Feature;
use Platform\Features\Features;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class EntryItemRefresherTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private EntryItemRefresher $entryItemRefresher;

    public function init()
    {
        $this->mockConsumer($user = $this->muffin(User::class));
        current_account()->defineFeatures(new Features([new Feature('order_payments', 'enabled')]));
        Setting::create(['key' => 'paid-entries', 'value' => 'submit']);
        $this->entryItemRefresher = app(EntryItemRefresher::class);
        $this->cart = Cart::open($user);
    }

    public function testItShouldRemoveWhenEntryIsTrashed(): void
    {
        $entry = new Entry;
        $entry->accountId = current_account_id();
        $entry->deletedAt = now();
        $entry->save();

        $entryItem = new EntryItem(new EntryAmount($entry, new Currency('AUD')), 'fakeName', new Price, true);

        $this->assertTrue($this->entryItemRefresher->shouldRemove($entryItem, $this->cart));
    }

    public function testItShouldRemoveWhenEntryHasAPaidOrder(): void
    {
        $entry = new Entry;
        $entry->accountId = current_account_id();
        $entry->save();

        $order = new Order;
        $order->accountId = current_account_id();
        $order->markAsPaid();
        $order->save();

        $orderItem = new OrderItem;
        $orderItem->orderId = $order->id;
        $orderItem->entryId = $entry->id;
        $orderItem->save();

        $entry->orderId = $order->id;
        $entry->save();

        $entryItem = new EntryItem(new EntryAmount($entry, new Currency('AUD')), 'fakeName', new Price, true);

        $this->assertTrue($this->entryItemRefresher->shouldRemove($entryItem, $this->cart));
    }

    public function testItShouldNotRemoveWhenEntryIsNotTrashedNorPaid(): void
    {
        $entry = new Entry;
        $entry->accountId = current_account_id();
        $entry->save();

        $entryItem = new EntryItem(new EntryAmount($entry, new Currency('AUD')), 'fakeName', new Price, true);

        $this->assertFalse($this->entryItemRefresher->shouldRemove($entryItem, $this->cart));
    }
}
