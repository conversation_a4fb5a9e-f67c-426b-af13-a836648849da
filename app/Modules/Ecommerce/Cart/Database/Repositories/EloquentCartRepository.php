<?php

namespace AwardForce\Modules\Ecommerce\Cart\Database\Repositories;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Ecommerce\Cart\BaseItem;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Database\Entities\CartItem;
use AwardForce\Modules\Ecommerce\Cart\Database\Entities\PersistentCart;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Traits\HasSeasonalBuilder;
use Carbon\CarbonImmutable;
use Eloquence\Behaviours\Slug;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentCartRepository extends Repository implements BuilderRepository, CartRepository
{
    use HasQueryBuilder;
    use HasSeasonalBuilder;

    public function __construct(PersistentCart $cart)
    {
        $this->model = $cart;
    }

    public function forUserInSeason(int $userId, int $seasonId): ?PersistentCart
    {
        $this->fields(['id', 'slug', 'user_id', 'season_id', 'tax_id', 'total', 'currency', 'parameters', 'created_at', 'status']);
        $this->user($userId);
        $this->season($seasonId);

        return $this->first();
    }

    public function user(int $userId): self
    {
        $this->query()->where('user_id', $userId);

        return $this;
    }

    public function slug(Slug $slug): CartRepository
    {
        $this->query()->where('slug', (string) $slug);

        return $this;
    }

    public function slugs(array $slugs): CartRepository
    {
        $this->query()->whereIn('slug', $slugs);

        return $this;
    }

    public function forUser(User $user, Account $account): Cart
    {
        $savedCart = $this->forUserInSeason($user->id, $account->activeSeason()->id);

        if ($savedCart) {
            return $this->openExisting($savedCart);
        }

        return Cart::open($user);
    }

    public function forSlug(Slug $slug): Cart
    {
        $this->fields(['id', 'slug', 'user_id', 'season_id', 'tax_id', 'total', 'currency', 'parameters', 'created_at', 'status']);
        $this->slug($slug);

        return $this->openExisting($this->require());
    }

    protected function openExisting(PersistentCart $savedCart): Cart
    {
        return Cart::openExisting(
            $savedCart->user,
            $savedCart->tax->asTaxRate($savedCart->vatNumber()),
            $currency = new Currency($savedCart->currency),
            $this->restoreItems($savedCart->items, $currency),
            $savedCart->parameters,
            new CarbonImmutable($savedCart->createdAt),
            $savedCart->status,
            $savedCart->slug,
        );
    }

    private function restoreItems(Collection $items, Currency $currency): array
    {
        return $items
            ->map(fn($item) => $item->toSimpleItem($currency))
            ->all();
    }

    public function save($cart)
    {
        DB::transaction(function () use ($cart) {
            $savedCart = $this->forUserInSeason($cart->userId(), $this->activeSeasonId());

            if ($savedCart) {
                $savedCart->update([
                    'tax_id' => $cart->taxRate()->id(),
                    'total' => $cart->total()->valueToCents(),
                    'currency' => $cart->currency()->code(),
                    'parameters' => $cart->params(),
                    'status' => $cart->status(),
                ]);
            } else {
                $savedCart = PersistentCart::createFrom($cart);
            }

            $savedCart->items()->delete();
            $savedCart->items()->saveMany($this->mapCartItems($cart->items()));
        });
    }

    private function mapCartItems(array $items): array
    {
        return array_map(fn(BaseItem $item) => CartItem::fromSimpleItem($item), $items);
    }

    public function empty(Cart $cart)
    {
        if ($savedCart = $this->forUserInSeason($cart->userId(), $this->activeSeasonId())) {
            $savedCart->delete();
        }
    }

    public function withoutTax(): self
    {
        $this->query()->whereNull('tax_id');

        return $this;
    }

    public function updateTax(int $newTaxId): void
    {
        $this->query()->update(['tax_id' => $newTaxId]);
    }
}
