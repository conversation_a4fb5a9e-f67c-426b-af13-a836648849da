<div class="row">
    <div class="col-xs-12 col-md-6">
        <div class="panel panel-default">
            <div class="panel-body">
                {!! html()->seasonSelector('tabs.form.season.label') !!}
                @if ($tab->id)
                    <div class="form-group">
                        <label for="type">
                            <span class="af-icons af-icons-lock"></span> {{ trans('tabs.form.type.label') }}
                        </label>
                        <input type="text" class="form-control" id="type" value="@lang('tabs.types.'.$tab->type)"
                               disabled>
                    </div>
                @endif

                {!! html()->formSelector() !!}
            </div>
        </div>

        @if (!$tab->id)
            <div class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="panel-title">
                        <h4>{{ trans('tabs.form.type.label') }}</h4>
                    </div>
                    <div class="form-group">
                        @foreach ($tabTypes as $key => $values)
                            <div class="radio styled">
                                {!! html()->radio('type', null, $key)->attributes(['id' => 'type_' . $key])->disabled($values['feature'] && feature_disabled($values['feature'])) !!}
                                <label for="type_{{ $key }}">
                                    <b>{{ $values['label'] }}</b>
                                </label>
                                @if($values['feature'] && feature_disabled($values['feature']))
                                    <a href="/feature-disabled/{{ $values['displayFeature'] ?? $values['feature']}}"
                                       target="_blank" rel="noopener noreferrer">@lang('shared.learn_more')</a>
                                @endif
                                <p class="man pls">@lang('tabs.types_descriptions.'.$key)</p>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <div class="panel panel-default">
            <div class="panel-body">
                <div class="panel-title">
                    <h4>{{ trans('tabs.types.Fields') }}</h4>
                </div>
                <div class="form-group horizontal">
                    {!! html()->label(trans('tabs.form.name.label'), 'name') !!}

                    {!! Multilingual::text('name', $tab, ['class' => 'form-control', 'disabled' => !empty($readOnly) ]) !!}
                </div>

                <div class="form-group horizontal">
                    <div class="mbs">
                        {!! html()->label(trans('tabs.form.content_block.label'), 'contentblockId') !!}
                        <help-icon content="{{ trans('tabs.form.content_block.help') }}"></help-icon>
                    </div>
                    {!! html()->select('contentblockId', for_select($contentBlocks, ['id', 'title'], true), null)->attributes(['class' => 'form-control'])->disabled(!empty($readOnly)) !!}
                </div>

                @if (!$tab->locked)
                    <div class="form-group horizontal">
                        <div class="mbs">
                            {!! html()->label(trans('tabs.form.order.label'), 'order') !!}
                            <help-icon content="{{ trans('tabs.form.order.help') }}"></help-icon>
                        </div>
                        {!! html()->number('order', $tabOrderPrefilled ?? null)->attributes(['class' => 'form-control'])->disabled(!empty($readOnly)) !!}
                    </div>
                @else
                    {!! html()->hidden('order') !!}
                @endif

                <div class="checkbox styled">
                    {!! html()->checkbox('visibleToEntrants', $tab->visibleToEntrants ?? true, true)->attributes(['id' => 'visibleToEntrants'])->disabled(((bool)$tab->locked || !empty($readOnly))) !!}
                    <label for="visibleToEntrants">
                        @lang('tabs.form.visible_to_entrants.label')
                    </label>
                </div>
                <div class="checkbox styled">
                    {!! html()->checkbox('tabDividerOnPdfs', null, true)->attributes(['id' => 'divider'])->disabled(!empty($readOnly)) !!}
                    <label for="divider">
                        @lang('tabs.form.tab_divider_on_pdfs.label')
                    </label>
                </div>
            </div>
        </div>

        @if (!$tab->locked)
            <div id="categoriesContainer" class="panel panel-default">
                <div class="panel-body">
                    <div class="panel-title">
                        <h4>{{ trans('tabs.form.categories.text') }}</h4>
                    </div>
                    <div class="form-group">
                        <div class="radio styled">
                            {!! html()->radio('categoryOption', null, 'all')->checked($tab->categoryOption == 'all' || empty($tab->categoryOption))->attributes(['id' => 'allCategories'])->disabled(!empty($readOnly)) !!}
                            <label for="allCategories">
                                {!! trans('tabs.form.categories_all.label') !!}
                            </label>
                        </div>
                        <div class="radio styled">
                            {!! html()->radio('categoryOption', null, 'select')->checked($tab->categoryOption == 'select')->attributes(['id' => 'selectCategories'])->disabled(!empty($readOnly)) !!}
                            <label for="selectCategories">
                                {!! trans('tabs.form.categories_select.label') !!}
                            </label>
                        </div>
                    </div>

                    <div id="categorySelectionContainer">
                        <multiselect
                            id="category-selector"
                            name="categories[]"
                            :options="@js($categories)"
                            :selected-options="@js(old('categories', $categoryIds))"
                            placeholder="{{ trans('tabs.form.categories.placeholder') }}"
                            select-all-label="{{ trans('multiselect.select_all') }}">
                        </multiselect>
                    </div>

                </div>
            </div>
        @else
            {!! html()->hidden('categoryOption', 'all') !!}
        @endif
    </div>

    <div class="col-xs-12 col-md-6">
        <div class="options contributors-options @if ($tab->type !== 'Contributors') hidden @endif">
            <div class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="panel-title">
                        <h4>{{ trans('tabs.types.Contributors') }}</h4>
                    </div>
                    <div class="form-group">
                        {!! html()->label(trans('tabs.form.max_contributors.label'), 'maxContributors') !!}
                        {!! html()->number('maxContributors', null)->attributes(['class' => 'form-control'])->disabled(!empty($readOnly)) !!}
                    </div>
                    <div class="form-group">
                        {!! html()->label(trans('tabs.form.min_contributors.label'), 'minContributors') !!}
                        {!! html()->number('minContributors', null)->attributes(['class' => 'form-control'])->disabled(!empty($readOnly)) !!}
                    </div>
                </div>
            </div>
        </div>

        <div class="options eligibility-options @if ($tab->type !== 'Eligibility') hidden @endif">
            <div class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="panel-title">
                        <h4>{{ trans('tabs.configuration.eligibility.assessment.label') }}</h4>
                    </div>
                    <scoring-input
                        id="min-eligibility-score"
                        name="setting[min-eligibility-score]"
                        class="form-group"
                        :value="{{ $tab->getSetting('min-eligibility-score', 0) }}"
                        label="{{ trans('tabs.form.min_eligibility_score.label') }}"
                    ></scoring-input>
                </div>
            </div>


            <div class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="panel-title">
                        <h4>{{ trans('tabs.configuration.eligibility.communication.label') }}</h4>
                    </div>
                    <div class="form-group">
                        <div class="checkbox styled">
                            {!! html()->checkbox('setting[ineligible-hide-tabs]', (bool) $tab->getSetting('ineligible-hide-tabs'), true)->attributes(['id' => 'ineligibleHideTabs']) !!}
                            <label for="ineligibleHideTabs">
                                @lang('tabs.form.ineligible_hide_other_tabs.label')
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        {!! html()->label(trans('tabs.form.eligible_content_block.label'), 'eligibleContentBlock') !!}
                        {!! html()->select('setting[eligible-content-block]', for_select($eligibleContentBlocks, ['slug', 'title']), $tab->getSetting('eligible-content-block', null))->attributes(['class' => 'form-control', 'id' => 'eligibleContentBlock']) !!}
                    </div>
                    <div class="form-group">
                        {!! html()->label(trans('tabs.form.ineligible_content_block.label'), 'ineligibleContentBlock') !!}
                        {!! html()->select('setting[ineligible-content-block]', for_select($ineligibleContentBlocks, ['slug', 'title']), $tab->getSetting('ineligible-content-block'))->attributes(['class' => 'form-control', 'id' => 'ineligibleContentBlock']) !!}
                    </div>
                    <div class="form-group">
                        {!! html()->label(trans('tabs.form.eligible_notification.label'), 'eligibleNotification') !!}
                        {!! html()->select('setting[eligible-notification]', for_select($eligibleNotifications, ['slug', 'subject'], true), $tab->getSetting('eligible-notification'))->attributes(['class' => 'form-control', 'id' => 'eligibleNotification']) !!}
                    </div>
                    <div class="form-group">
                        {!! html()->label(trans('tabs.form.ineligible_notification.label'), 'ineligibleNotification') !!}
                        {!! html()->select('setting[ineligible-notification]', for_select($ineligibleNotifications, ['slug', 'subject'], true), $tab->getSetting('ineligible-notification'))->attributes(['class' => 'form-control', 'id' => 'ineligibleNotification']) !!}
                    </div>
                </div>
            </div>
        </div>

        <div class="options referees-options @if($tab->type !== 'Referees') hidden @endif">
            <div class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="panel-title">
                        <h4>{{ trans('tabs.types.Referees') }}</h4>
                    </div>

                    <div class="form-group">
                        {!! html()->label(trans('review-flow.titles.action'), 'reviewStage') !!}
                        {!! html()->select('setting[review-stage]', for_select($refereesReviewStages, ['slug', 'name']), $tab->getSetting('review-stage', null))->attributes(['class' => 'form-control', 'id' => 'reviewStage']) !!}
                    </div>

                    <div class="form-group">
                        {!! html()->label(trans('tabs.form.max_referees.label'), 'max-referees') !!}
                        {!! html()->number('setting[max-referees]', $tab->getSetting('max-referees', ''))->attributes(['class' => 'form-control'])->disabled(!empty($readOnly)) !!}
                    </div>

                    <div class="form-group">
                        {!! html()->label(trans('tabs.form.min_referees.label'), 'min-referees') !!}
                        {!! html()->number('setting[min-referees]', $tab->getSetting('min-referees', ''))->attributes(['class' => 'form-control'])->disabled(!empty($readOnly)) !!}
                    </div>
                </div>
            </div>
        </div>


        <div class="options attachments-options @if ($tab->type !== 'Attachments') hidden @endif">
            <div class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="panel-title">
                        <h4>{{ trans('tabs.form.headers.attachment_limits') }}</h4>
                    </div>

                    <div class="form-group">
                        {!! html()->label(trans('setting.form.max_filesize.label'), 'maxFilesize') !!}
                        {!! html()->text('setting[max-filesize]', array_get($settings, 'max-filesize'))->attributes(['class' => 'form-control', 'id' => 'maxFilesize'])->disabled(!empty($readOnly)) !!}
                    </div>

                    <div class="form-group">
                        {!! html()->label(trans('setting.form.min_filesize.label'), 'minFilesize') !!}
                        {!! html()->text('setting[min-filesize]', array_get($settings, 'min-filesize'))->attributes(['class' => 'form-control', 'id' => 'minFilesize'])->disabled(!empty($readOnly)) !!}
                    </div>

                    <div class="form-group">
                        {!! html()->label(trans('setting.form.max_attachments.label'), 'maxAttachments') !!}
                        {!! html()->text('setting[max-attachments]', array_get($settings, 'max-attachments'))->attributes(['class' => 'form-control', 'id' => 'maxAttachments'])->disabled(!empty($readOnly)) !!}
                    </div>

                    <div class="form-group">
                        {!! html()->label(trans('setting.form.min_attachments.label'), 'minAttachments') !!}
                        {!! html()->text('setting[min-attachments]', array_get($settings, 'min-attachments'))->attributes(['class' => 'form-control', 'id' => 'minAttachments'])->disabled(!empty($readOnly)) !!}
                    </div>

                    <div class="form-group">
                        <div class="checkbox styled">
                            {!! html()->hidden('setting[accept-attachment-links]', 0) !!}
                            {!! html()->checkbox('setting[accept-attachment-links]', array_get($settings, 'accept-attachment-links'), 1)->attributes(['id' => 'accept-attachment-link'])->disabled(!empty($readOnly)) !!}
                            <label for="accept-attachment-link">
                                {{ trans('setting.form.accept_attachment_links.label') }}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="options attachments-options @if ($tab->type !== 'Attachments') hidden @endif">
    @include('field.partials.allowed-file-types', [
        'name' => 'extensions',
        'grouped' => true,
        'minVideoLength' => array_get($settings, 'min-video-length'),
        'minVideoLengthFieldName' => 'setting[min-video-length]',
        'maxVideoLength' => array_get($settings, 'max-video-length'),
        'maxVideoLengthFieldName' => 'setting[max-video-length]',
        'maxImageWidthFieldName' => 'imageDimensionConstraints[maxWidth]',
        'minImageWidthFieldName' => 'imageDimensionConstraints[minWidth]',
        'maxImageHeightFieldName' => 'imageDimensionConstraints[maxHeight]',
        'minImageHeightFieldName' => 'imageDimensionConstraints[minHeight]',
        'imageDimensionConstraints' => $tab->imageDimensionConstraints,
    ])
</div>
<script type="text/javascript">
    var seasonEligibleNotifications = '{{ obfuscate($seasonEligibleNotifications->map(fn($n) => for_select($n, ['slug', 'subject'], true))) }}';
    var seasonIneligibleNotifications = '{{ obfuscate($seasonIneligibleNotifications->map(fn($n) => for_select($n, ['slug', 'subject'], true))) }}';
</script>
