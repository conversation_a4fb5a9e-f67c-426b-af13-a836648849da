import {
	createUploadedFile,
	getDefaultUploadedFile,
	Resource,
	TranscodeStatus,
	UploadedFile,
	UploadedFileProps,
	UploaderStatus,
} from '@/lib/types/UploadedFile';
import { describe, expect, it } from 'vitest';

describe('UploadedFile', () => {
	it('should create an UploadedFile object with the provided properties', () => {
		const uploadedFileProps = {
			attachmentId: 123,
			file: 'example-file',
			foreignId: 1,
			id: 2,
			image: null,
			loaded: 50,
			mime: 'image/jpeg',
			name: 'example-name',
			oldId: 3,
			original: 'example-original',
			percent: 75,
			remoteId: 3,
			resource: Resource.AVATAR,
			resourceId: 4,
			size: 1024,
			slug: 'example-slug',
			source: 'example-source',
			status: UploaderStatus.COMPLETED,
			statusMessage: 'Transcoding completed successfully',
			tabId: 5,
			token: 'example-token',
			transcodingErrors: ['Error 1', 'Error 2'],
			transcodingStatus: TranscodeStatus.TRANSCODE_STATUS_COMPLETED,
			url: 'example-url',
		};

		const createdUploadedFile: UploadedFile = createUploadedFile(uploadedFileProps);
		expect(createdUploadedFile).toStrictEqual(uploadedFileProps);
	});

	it('should create an UploadedFile object with default values if no properties are provided', () => {
		const defaultUploadedFile: UploadedFile = createUploadedFile({} as UploadedFileProps);

		const expectedDefaultValues = getDefaultUploadedFile();

		expect(defaultUploadedFile.file).toEqual(expectedDefaultValues.file);
		expect(defaultUploadedFile.foreignId).toEqual(expectedDefaultValues.foreignId);
		expect(defaultUploadedFile.id).toEqual(expectedDefaultValues.id);
		expect(defaultUploadedFile.image).toEqual(expectedDefaultValues.image);
		expect(defaultUploadedFile.loaded).toEqual(expectedDefaultValues.loaded);
		expect(defaultUploadedFile.mime).toEqual(expectedDefaultValues.mime);
		expect(defaultUploadedFile.name).toEqual(expectedDefaultValues.name);
		expect(defaultUploadedFile.original).toEqual(expectedDefaultValues.original);
		expect(defaultUploadedFile.percent).toEqual(expectedDefaultValues.percent);
		expect(defaultUploadedFile.remoteId).toEqual(expectedDefaultValues.remoteId);
		expect(defaultUploadedFile.resource).toEqual(expectedDefaultValues.resource);
		expect(defaultUploadedFile.resourceId).toEqual(expectedDefaultValues.resourceId);
		expect(defaultUploadedFile.size).toEqual(expectedDefaultValues.size);
		expect(defaultUploadedFile.slug).toEqual(expectedDefaultValues.slug);
		expect(defaultUploadedFile.source).toEqual(expectedDefaultValues.source);
		expect(defaultUploadedFile.status).toEqual(expectedDefaultValues.status);
		expect(defaultUploadedFile.statusMessage).toEqual(expectedDefaultValues.statusMessage);
		expect(defaultUploadedFile.tabId).toEqual(expectedDefaultValues.tabId);
		expect(defaultUploadedFile.token).toEqual(expectedDefaultValues.token);
		expect(defaultUploadedFile.transcodingErrors).toEqual(expectedDefaultValues.transcodingErrors);
		expect(defaultUploadedFile.transcodingStatus).toEqual(expectedDefaultValues.transcodingStatus);
		expect(defaultUploadedFile.url).toEqual(expectedDefaultValues.url);
	});
});
