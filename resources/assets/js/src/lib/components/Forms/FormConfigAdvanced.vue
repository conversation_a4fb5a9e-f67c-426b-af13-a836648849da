<template>
	<div>
		<!-- Home -->
		<settings
			v-if="homeSettings && Object.keys(homeSettings).length > 0"
			:section="lang.get(`form.form.settings.sections.home`)"
			:model-value="modelSettings"
			:settings="homeSettings"
		/>

		<!-- Manage -->
		<settings
			v-if="manageSettings && Object.keys(manageSettings).length > 0"
			:section="lang.get(`form.form.settings.sections.manage`)"
			:model-value="modelSettings"
			:settings="manageSettings"
		/>

		<!-- Role Visibility -->
		<settings
			v-if="roleSetting && Object.keys(roleSetting).length > 0"
			:model-value="modelSettings"
			:section="lang.get('form.form.role.visibility.label')"
			:settings="roleSetting"
		>
			<template #role-setting>
				<role-visibility :all-roles="formRoles" :selected-roles="selectedRoles" :setting="roleSetting" />
			</template>
		</settings>

		<!-- Application/Grants -->
		<settings
			v-if="applicationSettings && Object.keys(applicationSettings).length > 0"
			:section="lang.get(`form.form.settings.sections.` + formType)"
			:model-value="modelSettings"
			:settings="applicationSettings"
		>
			<template #content-blocks>
				<form-content-block-selector :items="contentBlocks" :value="selectedContentBlock" @update="updateContentBlockId" />
			</template>
		</settings>
	</div>
</template>

<script lang="ts">
import type { ContentBlock } from '@/domain/models/Tab';
import { defineComponent, PropType } from 'vue';
import { useController } from '@/domain/services/Composer';
import FormContentBlockSelector from '@/lib/components/Forms/FormContentBlockSelector.vue';
import RoleVisibility from '@/lib/components/Forms/RoleVisibility.vue';
import Settings from '@/lib/components/Settings/Settings.vue';
import { formConfigAdvancedController } from '@/lib/components/Forms/FormConfigAdvanced.controller';
import type { FormSettings, FormType } from '@/domain/models/Form';

export default defineComponent({
	components: {
		Settings,
		FormContentBlockSelector,
		RoleVisibility,
	},
	props: {
		settings: {
			type: Object as PropType<FormSettings>,
			required: true,
		},
		formType: {
			type: String as () => FormType,
			required: true,
		},
		contentBlocks: {
			type: Array as () => ContentBlock[],
			default: () => [],
		},
		selectedContentBlock: {
			type: String,
			default: '',
		},
		formRoles: {
			type: Array as () => Partial<RoleSettings>[],
			default: () => [],
		},
		selectedRoles: {
			type: Array as () => string[],
			default: () => [],
		},
	},
	setup: useController(formConfigAdvancedController, 'formConfigAdvancedController'),
});
</script>
