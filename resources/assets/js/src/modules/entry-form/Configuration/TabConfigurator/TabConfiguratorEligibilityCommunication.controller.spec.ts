import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import {
	Props,
	tabConfiguratorEligibilityCommunicationController,
	View,
} from '@/modules/entry-form/Configuration/TabConfigurator/TabConfiguratorEligibilityCommunication.controller';

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

describe('TabConfiguratorEligibilityCommunication controller', () => {
	const mockOnMounted = vi.fn();
	const mockUpdateTab = vi.fn();
	const mockGetEligibleContentBlocks = vi.fn();
	const mockGetIneligibleContentBlocks = vi.fn();
	const mockGetEligibleNotifications = vi.fn();
	const mockGetIneligibleNotifications = vi.fn();
	const mockEmit = vi.fn();

	beforeEach(() => {
		vi.clearAllMocks();

		// Mock useEntryFormContainer
		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable: {
				updateTab: mockUpdateTab,
				getEligibleContentBlocks: mockGetEligibleContentBlocks,
				getIneligibleContentBlocks: mockGetIneligibleContentBlocks,
				getEligibleNotifications: mockGetEligibleNotifications,
				getIneligibleNotifications: mockGetIneligibleNotifications,
			},
			onMounted: mockOnMounted,
		});
	});

	it('should return correct view properties', () => {
		const mockTab = {
			id: '1',
			eligibleContentBlock: 'eligibleContentBlock',
			eligibleNotification: 'eligibleNotification',
			ineligibleContentBlock: 'ineligibleContentBlock',
			ineligibleNotification: 'ineligibleNotification',
		};

		const mockEligibleContentBlocks = [{ id: 'block1' }, { id: 'block2' }];
		const mockIneligibleContentBlocks = [{ id: 'block3' }, { id: 'block4' }];
		const mockEligibleNotifications = [{ id: 'notif1' }, { id: 'notif2' }];
		const mockIneligibleNotifications = [{ id: 'notif3' }, { id: 'notif4' }];

		mockGetEligibleContentBlocks.mockReturnValue(mockEligibleContentBlocks);
		mockGetIneligibleContentBlocks.mockReturnValue(mockIneligibleContentBlocks);
		mockGetEligibleNotifications.mockReturnValue(mockEligibleNotifications);
		mockGetIneligibleNotifications.mockReturnValue(mockIneligibleNotifications);

		const props: Props = { tab: mockTab };
		const view = tabConfiguratorEligibilityCommunicationController(props, { emit: mockEmit }) as View;

		expect(view.eligibleContentBlocks).toBe(mockEligibleContentBlocks);
		expect(view.ineligibleContentBlocks).toBe(mockIneligibleContentBlocks);
		expect(view.eligibleNotifications).toBe(mockEligibleNotifications);
		expect(view.ineligibleNotifications).toBe(mockIneligibleNotifications);
	});

	it('should call onMounted when controller is initialized', () => {
		const props: Props = { tab: { id: '1' } as any };
		tabConfiguratorEligibilityCommunicationController(props, { emit: mockEmit });

		expect(mockOnMounted).toHaveBeenCalledTimes(1);
		expect(typeof mockOnMounted.mock.calls[0][0]).toBe('function');
	});

	it('should set default values when tab properties are null', () => {
		const mockTab = {
			id: '1',
			eligibleContentBlock: null,
			eligibleNotification: null,
			ineligibleContentBlock: null,
			ineligibleNotification: null,
		};

		const mockEligibleContentBlocks = [{ id: 'default-block' }];
		const mockIneligibleContentBlocks = [{ id: 'default-ineligible-block' }];
		const mockEligibleNotifications = [{ id: 'default-notif' }];
		const mockIneligibleNotifications = [{ id: 'default-ineligible-notif' }];

		mockGetEligibleContentBlocks.mockReturnValue(mockEligibleContentBlocks);
		mockGetIneligibleContentBlocks.mockReturnValue(mockIneligibleContentBlocks);
		mockGetEligibleNotifications.mockReturnValue(mockEligibleNotifications);
		mockGetIneligibleNotifications.mockReturnValue(mockIneligibleNotifications);

		const props: Props = { tab: mockTab };
		tabConfiguratorEligibilityCommunicationController(props, { emit: mockEmit });

		// Get the onMounted callback
		const onMountedCallback = mockOnMounted.mock.calls[0][0];
		onMountedCallback();

		expect(mockUpdateTab).toHaveBeenCalledWith({
			...mockTab,
			eligibleContentBlock: 'default-block',
			ineligibleContentBlock: 'default-ineligible-block',
			eligibleNotification: 'default-notif',
			ineligibleNotification: 'default-ineligible-notif',
		});
	});

	it('should set default values when tab properties are undefined', () => {
		const mockTab = {
			id: '1',
			eligibleContentBlock: undefined,
			eligibleNotification: undefined,
			ineligibleContentBlock: undefined,
			ineligibleNotification: undefined,
		};

		const mockEligibleContentBlocks = [{ id: 'default-block' }];
		const mockIneligibleContentBlocks = [{ id: 'default-ineligible-block' }];
		const mockEligibleNotifications = [{ id: 'default-notif' }];
		const mockIneligibleNotifications = [{ id: 'default-ineligible-notif' }];

		mockGetEligibleContentBlocks.mockReturnValue(mockEligibleContentBlocks);
		mockGetIneligibleContentBlocks.mockReturnValue(mockIneligibleContentBlocks);
		mockGetEligibleNotifications.mockReturnValue(mockEligibleNotifications);
		mockGetIneligibleNotifications.mockReturnValue(mockIneligibleNotifications);

		const props: Props = { tab: mockTab };
		tabConfiguratorEligibilityCommunicationController(props, { emit: mockEmit });

		// Get the onMounted callback
		const onMountedCallback = mockOnMounted.mock.calls[0][0];
		onMountedCallback();

		expect(mockUpdateTab).toHaveBeenCalledWith({
			...mockTab,
			eligibleContentBlock: 'default-block',
			ineligibleContentBlock: 'default-ineligible-block',
			eligibleNotification: 'default-notif',
			ineligibleNotification: 'default-ineligible-notif',
		});
	});

	it('should handle empty arrays and set null values', () => {
		const mockTab = {
			id: '1',
			eligibleContentBlock: null,
			eligibleNotification: null,
			ineligibleContentBlock: null,
			ineligibleNotification: null,
		};

		mockGetEligibleContentBlocks.mockReturnValue([]);
		mockGetIneligibleContentBlocks.mockReturnValue([]);
		mockGetEligibleNotifications.mockReturnValue([]);
		mockGetIneligibleNotifications.mockReturnValue([]);

		const props: Props = { tab: mockTab };
		tabConfiguratorEligibilityCommunicationController(props, { emit: mockEmit });

		// Get the onMounted callback
		const onMountedCallback = mockOnMounted.mock.calls[0][0];
		onMountedCallback();

		expect(mockUpdateTab).toHaveBeenCalledWith({
			...mockTab,
			eligibleContentBlock: null,
			ineligibleContentBlock: null,
			eligibleNotification: null,
			ineligibleNotification: null,
		});
	});

	it('should preserve existing values when they are already set', () => {
		const mockTab = {
			id: '1',
			eligibleContentBlock: 'existing-block',
			eligibleNotification: 'existing-notif',
			ineligibleContentBlock: 'existing-ineligible-block',
			ineligibleNotification: 'existing-ineligible-notif',
		};

		const mockEligibleContentBlocks = [{ id: 'new-block' }];
		const mockIneligibleContentBlocks = [{ id: 'new-ineligible-block' }];
		const mockEligibleNotifications = [{ id: 'new-notif' }];
		const mockIneligibleNotifications = [{ id: 'new-ineligible-notif' }];

		mockGetEligibleContentBlocks.mockReturnValue(mockEligibleContentBlocks);
		mockGetIneligibleContentBlocks.mockReturnValue(mockIneligibleContentBlocks);
		mockGetEligibleNotifications.mockReturnValue(mockEligibleNotifications);
		mockGetIneligibleNotifications.mockReturnValue(mockIneligibleNotifications);

		const props: Props = { tab: mockTab };
		tabConfiguratorEligibilityCommunicationController(props, { emit: mockEmit });

		// Get the onMounted callback
		const onMountedCallback = mockOnMounted.mock.calls[0][0];
		onMountedCallback();

		expect(mockUpdateTab).toHaveBeenCalledWith({
			...mockTab,
			eligibleContentBlock: 'existing-block',
			ineligibleContentBlock: 'existing-ineligible-block',
			eligibleNotification: 'existing-notif',
			ineligibleNotification: 'existing-ineligible-notif',
		});
	});
});
