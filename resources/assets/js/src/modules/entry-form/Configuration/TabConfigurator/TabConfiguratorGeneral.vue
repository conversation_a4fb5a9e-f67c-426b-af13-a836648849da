<template>
	<filtertron-tray-section id="tab-configurator-general" :title="lang.get('tabs.configuration.general.label')">
		<div class="fields island">
			<div class="form-group">
				<label>
					{{ lang.get('tabs.form.name.label') }}
				</label>
				<multilingual
					:supported-languages="supportedLanguages"
					:resource="tab"
					property="name"
					@input="(translated) => handleTranslatedInput('name', translated)"
				/>
			</div>
			<div class="form-group">
				<label for="content-blocks">
					{{ lang.get('tabs.form.content_block.label') }}
				</label>
				<select
					id="content-blocks"
					class="form-control"
					@change="(e) => handleInput('contentblockId', e.target.value ? parseInt(e.target.value) : null)"
				>
					<option></option>
					<option
						v-for="contentBlock in contentBlocks"
						:key="'content-block-' + contentBlock.id"
						:value="contentBlock.id"
						:selected="contentBlock.id === tab.contentblockId"
					>
						{{ contentBlock.title }}
					</option>
				</select>
			</div>
			<div class="checkbox styled">
				<input
					id="visible-to-entrants"
					name="visibleToEntrants"
					type="checkbox"
					:checked="tab.visibleToEntrants"
					:disabled="tab.locked"
					@change="(e) => handleInput('visibleToEntrants', e.target.checked)"
				/>
				<label for="visible-to-entrants">
					{{ lang.get('tabs.form.visible_to_entrants.label') }}
				</label>
			</div>
			<div class="checkbox styled">
				<input
					id="tab-divider-on-pdfs"
					name="tabDividerOnPdfs"
					type="checkbox"
					:checked="tab.tabDividerOnPdfs"
					@change="(e) => handleInput('tabDividerOnPdfs', e.target.checked)"
				/>
				<label for="tab-divider-on-pdfs">
					{{ lang.get('tabs.form.tab_divider_on_pdfs.label') }}
				</label>
			</div>
		</div>
	</filtertron-tray-section>
</template>

<script>
import { mapState } from 'vuex';
import FiltertronTraySection from '@/lib/components/Filtertron/FiltertronTraySection';
import Multilingual from '@/lib/components/Translations/Multilingual';
import handleInputMixin from '../handle-input-mixin.js';
import handleTranslatedInputMixin from '../handle-translated-input-mixin.js';
import ScoringInput from '@/lib/components/Fields/ScoringInput';

export default {
	inject: ['lang'],
	components: {
		FiltertronTraySection,
		Multilingual,
		ScoringInput,
	},
	mixins: [handleInputMixin, handleTranslatedInputMixin],
	props: {
		tab: {
			type: Object,
			required: true,
		},
	},
	computed: {
		...mapState('entryForm', ['contentBlocks']),
	},
};
</script>
