<template>
	<filtertron-tray-section
		v-if="tab.type === 'Eligibility'"
		id="tab-configurator-Eligibility-communication"
		:title="lang.get('tabs.configuration.eligibility.communication.label')"
	>
		<div class="fields island">
			<div class="form-group">
				<div class="checkbox styled">
					<input
						id="ineligible-hide-tabs"
						name="ineligibleHideTabs"
						type="checkbox"
						:checked="tab.ineligibleHideTabs"
						@change="(e) => handleInput('ineligibleHideTabs', e.target.checked)"
					/>
					<label for="ineligible-hide-tabs">
						{{ lang.get('tabs.form.ineligible_hide_other_tabs.label') }}
					</label>
				</div>
				<label>{{ lang.get('tabs.form.eligible_content_block.label') }}</label>
				<select
					id="eligible-content-blocks"
					class="form-control"
					@change="(e) => handleInput('eligibleContentBlock', e.target.value)"
				>
					<option
						v-for="eligibleContentBlock in eligibleContentBlocks"
						:key="'eligible-content-block-' + eligibleContentBlock.id"
						:value="eligibleContentBlock.id"
						:selected="eligibleContentBlock.id === tab.eligibleContentBlock"
					>
						{{ eligibleContentBlock.title }}
					</option>
				</select>
			</div>
			<div class="form-group">
				<label>{{ lang.get('tabs.form.ineligible_content_block.label') }}</label>
				<select
					id="ineligible-content-blocks"
					class="form-control"
					@change="(e) => handleInput('ineligibleContentBlock', e.target.value)"
				>
					<option
						v-for="ineligibleContentBlock in ineligibleContentBlocks"
						:key="'ineligible-content-block-' + ineligibleContentBlock.id"
						:value="ineligibleContentBlock.id"
						:selected="ineligibleContentBlock.id === tab.ineligibleContentBlock"
					>
						{{ ineligibleContentBlock.title }}
					</option>
				</select>
			</div>
			<div class="form-group">
				<label>{{ lang.get('tabs.form.eligible_notification.label') }}</label>
				<select
					id="eligible-notifications"
					class="form-control"
					@change="(e) => handleInput('eligibleNotification', e.target.value)"
				>
					<option
						v-for="eligibleNotification in eligibleNotifications"
						:key="'eligible-notification-' + eligibleNotification.id"
						:value="eligibleNotification.id"
						:selected="eligibleNotification.id === tab.eligibleNotification"
					>
						{{ eligibleNotification.subject }}
					</option>
				</select>
			</div>
			<div class="form-group">
				<label>{{ lang.get('tabs.form.ineligible_notification.label') }}</label>
				<select
					id="ineligible-notifications"
					class="form-control"
					@change="(e) => handleInput('ineligibleNotification', e.target.value)"
				>
					<option
						v-for="ineligibleNotification in ineligibleNotifications"
						:key="'ineligible-notification-' + ineligibleNotification.id"
						:value="ineligibleNotification.id"
						:selected="ineligibleNotification.id === tab.ineligibleNotification"
					>
						{{ ineligibleNotification.subject }}
					</option>
				</select>
			</div>
		</div>
	</filtertron-tray-section>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import FiltertronTraySection from '@/lib/components/Filtertron/FiltertronTraySection.vue';
import { useController } from '@/domain/services/Composer';
import {
	tabConfiguratorEligibilityCommunicationController,
	Props,
	View,
} from '@/modules/entry-form/Configuration/TabConfigurator/TabConfiguratorEligibilityCommunication.controller';

export default defineComponent<Props, View>({
	inject: ['lang'],
	components: { FiltertronTraySection },
	props: {
		tab: {
			type: Object,
			required: true,
		},
	},
	setup: useController(
		tabConfiguratorEligibilityCommunicationController,
		'TabConfiguratorEligibilityCommunicationController'
	) as () => View,
});
</script>
