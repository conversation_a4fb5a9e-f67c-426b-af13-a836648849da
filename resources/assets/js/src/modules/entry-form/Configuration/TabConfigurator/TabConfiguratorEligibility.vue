<template>
	<filtertron-tray-section
		v-if="tab.type === 'Eligibility'"
		id="tab-configurator-Eligibility-assessment"
		:title="lang.get('tabs.configuration.eligibility.assessment.label')"
	>
		<div class="fields island">
			<div class="form-group">
				<scoring-input
					id="min-eligibility-score"
					:value="tab.minEligibilityScore"
					:label="lang.get('tabs.form.min_eligibility_score.label')"
					@input="(v) => handleInput('minEligibilityScore', v)"
				></scoring-input>
			</div>
		</div>
	</filtertron-tray-section>
</template>

<script>
import handleInputMixin from '../handle-input-mixin.js';
import ScoringInput from '@/lib/components/Fields/ScoringInput.vue';
import FiltertronTraySection from '@/lib/components/Filtertron/FiltertronTraySection.vue';

export default {
	components: { FiltertronTraySection, ScoringInput },
	inject: ['lang'],
	mixins: [handleInputMixin],
	props: {
		tab: {
			type: Object,
			required: true,
		},
	},
};
</script>
