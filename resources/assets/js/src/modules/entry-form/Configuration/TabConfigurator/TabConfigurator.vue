<template>
	<div v-if="tab" class="configurator">
		<tab-type :tab="tab" @input="onInput" />
		<tab-type-feature-disabled-alert v-if="disabledEligibility" key="eligibility" feature="eligibility" />
		<tab-type-feature-disabled-alert
			v-if="disabledReferees"
			key="referee"
			feature="review_flow"
			display-feature="referee"
		/>

		<template v-if="canConfigure">
			<tab-configurator-general :tab="tab" @input="onInput" />
			<tab-configurator-categories v-if="isEntryForm" :tab="tab" @input="onInput" />
			<tab-configurator-eligibility :tab="tab" @input="onInput"></tab-configurator-eligibility>
			<tab-configurator-eligibility-communication :tab="tab" @input="onInput"></tab-configurator-eligibility-communication>
			<tab-configurator-contributors :tab="tab" @input="onInput" />
			<tab-configurator-referees :tab="tab" @input="onInput" />
			<tab-configurator-attachments :tab="tab" @input="onInput" />
			<tab-configurator-file-types :tab="tab" @input="onInput" />
		</template>

		<configuration-tray-buttons
			:disabled="!canConfigure"
			:show-delete-button="isDeletable"
			@saved="save"
			@canceled="onCanceled"
			@deleted="confirmDeletion = true"
		/>

		<confirmation-modal
			modal-id="modal-target-delete-tab"
			:show-modal="confirmDeletion"
			:confirmation="lang.get('tabs.alerts.delete')"
			:confirm-button-label="lang.get('buttons.delete')"
			:cancel-button-label="lang.get('buttons.cancel')"
			@closed="confirmDeletion = false"
			@confirmed="remove"
		/>

		<confirmation-modal
			modal-id="modal-target-cancel-tab-configuration"
			:show-modal="confirmCancellation"
			:confirmation="lang.get('miscellaneous.unsaved_changes')"
			:confirm-button-label="lang.get('buttons.continue')"
			:cancel-button-label="lang.get('buttons.cancel')"
			@closed="confirmCancellation = false"
			@confirmed="cancel"
		/>
	</div>
</template>

<script>
import _ from 'underscore';
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex';
import ConfirmationModal from '@/lib/components/Shared/ConfirmationModal';
import ConfigurationTrayButtons from '../ConfigurationTrayButtons';
import TabConfiguratorGeneral from './TabConfiguratorGeneral';
import TabConfiguratorCategories from './TabConfiguratorCategories';
import TabConfiguratorContributors from './TabConfiguratorContributors';
import TabConfiguratorAttachments from './TabConfiguratorAttachments';
import TabConfiguratorFileTypes from './TabConfiguratorFileTypes';
import TabType from './TabType';
import TabConfiguratorReferees from '@/modules/entry-form/Configuration/TabConfigurator/TabConfiguratorReferees.vue';
import { isNull } from '@/domain/utils/TypePredicates';
import TabTypeFeatureDisabledAlert from '@/modules/entry-form/Configuration/TabConfigurator/TabTypeFeatureDisabledAlert.vue';
import TabConfiguratorEligibility from '@/modules/entry-form/Configuration/TabConfigurator/TabConfiguratorEligibility.vue';
import TabConfiguratorEligibilityCommunication from '@/modules/entry-form/Configuration/TabConfigurator/TabConfiguratorEligibilityCommunication.vue';

export default {
	inject: ['lang', 'featuresService'],
	components: {
		TabConfiguratorEligibility,
		TabConfiguratorEligibilityCommunication,
		TabTypeFeatureDisabledAlert,
		TabConfiguratorReferees,
		ConfigurationTrayButtons,
		ConfirmationModal,
		TabConfiguratorGeneral,
		TabConfiguratorCategories,
		TabConfiguratorContributors,
		TabConfiguratorAttachments,
		TabConfiguratorFileTypes,
		TabType,
	},
	props: {
		tab: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			confirmCancellation: false,
			confirmDeletion: false,
		};
	},
	computed: {
		...mapState('entryFormConfiguration', ['configurationInProgress', 'saving']),
		...mapGetters('entryForm', ['isEntryForm']),
		isDeletable() {
			return !isNull(this.tab.createdAt) && this.tab.type !== 'Details';
		},
		disabledEligibility() {
			return this.tab.type === 'Eligibility' && !this.featuresService.enabled('eligibility');
		},
		disabledReferees() {
			return this.tab.type === 'Referees' && !this.featuresService.enabled('review_flow');
		},
		canConfigure() {
			return !this.disabledEligibility && !this.disabledReferees;
		},
	},
	created() {
		if (this.tab.createdAt) {
			this.preserveTab({ ...this.tab });
		}

		this.debouncedUpdate = _.debounce(this.updateTab, 300);

		this.$emit('created');
	},
	methods: {
		...mapActions('entryFormConfiguration', ['configureTab', 'addTab', 'saveTab', 'deleteTab']),
		...mapMutations('entryForm', ['updateTab', 'removeTab']),
		...mapMutations('entryFormConfiguration', ['preserveTab', 'setConfigurationInProgressFlag']),
		onInput(input) {
			if (!this.configurationInProgress) {
				this.setConfigurationInProgressFlag(true);
			}

			this.debouncedUpdate({ ...this.tab, ...input });

			setTimeout(() => {
				this.$emit('resizeTray');
			}, 50);
		},
		save() {
			if (!this.tab.createdAt) {
				this.addTab({ tab: this.tab });
			} else {
				this.saveTab({ tab: this.tab });
			}
		},
		onCanceled() {
			if (this.configurationInProgress) {
				this.confirmCancellation = true;
			} else {
				this.cancel();
			}
		},
		cancel() {
			this.confirmCancellation = false;
			this.$nextTick(() => {
				this.configureTab({ slug: null });
			});
		},
		remove() {
			this.confirmDeletion = false;

			this.deleteTab({ tab: this.tab }).then((response) => {
				if (response && response.status === 200) {
					this.configureTab({ slug: null });
					this.removeTab(this.tab.slug);
				}
			});
		},
	},
};
</script>

<style scoped>
.configurator {
	margin-bottom: 150px;
	display: flex;
	flex-direction: column;
	gap: 20px;

	& > * {
		background-color: #fff;
		border-color: #ddd;
		border-style: solid;
		border-width: 1px 0;
	}
}
</style>
