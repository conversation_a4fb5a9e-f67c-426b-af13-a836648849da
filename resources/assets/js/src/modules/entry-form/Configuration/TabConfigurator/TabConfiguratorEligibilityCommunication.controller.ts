import { EventEmitters } from '@/domain/utils/Events';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { ContentBlock, Notification, Tab as TabModel } from '@/domain/models/Tab';

enum Events {
	Input = 'input',
}

type Emitters = EventEmitters<{
	[Events.Input]: (property: string, value: string) => void;
}>;

type Tab = TabModel & {
	eligibleContentBlock: string | null;
	eligibleNotification: string | null;
	ineligibleContentBlock: string | null;
	ineligibleNotification: string | null;
};

type Props = {
	tab: Tab;
};

type View = {
	eligibleContentBlocks: ContentBlock[];
	ineligibleContentBlocks: ContentBlock[];
	eligibleNotifications: Notification[];
	ineligibleNotifications: Notification[];
	updateTab: (tab: Tab) => void;
	handleInput: (property: string, value: string) => void;
};

const tabConfiguratorEligibilityCommunicationController = (props: Props, { emit }: { emit: Emitters }): View => {
	// eslint-disable-next-line @typescript-eslint/naming-convention
	const { Submittable, onMounted } = useEntryFormContainer();

	onMounted(() => {
		const tab = { ...props.tab };
		if (!tab.eligibleContentBlock) {
			tab.eligibleContentBlock = Submittable.getEligibleContentBlocks()[0]?.id || null;
		}

		if (!tab.ineligibleContentBlock) {
			tab.ineligibleContentBlock = Submittable.getIneligibleContentBlocks()[0]?.id || null;
		}

		if (!tab.eligibleNotification) {
			tab.eligibleNotification = Submittable.getEligibleNotifications()[0]?.id || null;
		}

		if (!tab.ineligibleNotification) {
			tab.ineligibleNotification = Submittable.getIneligibleNotifications()[0]?.id || null;
		}

		Submittable.updateTab(tab);
	});

	const handleInput = (property: string, value: string) => {
		emit(Events.Input, property, value);
	};

	return {
		eligibleContentBlocks: Submittable.getEligibleContentBlocks(),
		ineligibleContentBlocks: Submittable.getIneligibleContentBlocks(),
		eligibleNotifications: Submittable.getEligibleNotifications(),
		ineligibleNotifications: Submittable.getIneligibleNotifications(),
		updateTab: Submittable.updateTab,
		handleInput,
	};
};

export { Props, View, tabConfiguratorEligibilityCommunicationController, Tab };
