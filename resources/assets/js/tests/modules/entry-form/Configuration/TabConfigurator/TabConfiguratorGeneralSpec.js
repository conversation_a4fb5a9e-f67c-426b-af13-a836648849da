import { expect } from 'chai';
import Multilingual from '@/lib/components/Translations/Multilingual.vue';
import TabConfiguratorGeneral from '@/modules/entry-form/Configuration/TabConfigurator/TabConfiguratorGeneral.vue';
import Vuex from 'vuex';
import { createLocalVue, shallowMount } from '@vue/test-utils';

const localVue = createLocalVue();
localVue.use(Vuex);

const store = new Vuex.Store({
	modules: {
		entryForm: {
			namespaced: true,
			state: {
				contentBlocks: [],
			},
		},
		global: {
			namespaced: true,
			state: {
				supportedLanguages: ['en_GB'],
				defaultLanguage: 'en_GB',
			},
		},
	},
});

const lang = { get: () => '' };

const newTabConfiguratorGeneral = (propsData) =>
	shallowMount(TabConfiguratorGeneral, {
		provide: { lang },
		propsData: {
			...propsData,
		},
		store,
		localVue,
	});

describe('TabConfiguratorGeneral', () => {
	it('shows configuration options no eligibility', () => {
		const tabConfiguratorGeneral = newTabConfiguratorGeneral({
			tab: {
				id: 1,
				translated: {},
				type: 'Details',
			},
		});

		expect(tabConfiguratorGeneral.findAllComponents(Multilingual).length).to.equal(1);
		expect(tabConfiguratorGeneral.findAll('select').length).to.equal(1);
		expect(tabConfiguratorGeneral.findAll('input[type=checkbox]').length).to.equal(2);

		// Visible only in eligibility type
		expect(tabConfiguratorGeneral.findAll('#min-eligibility-score').length).to.equal(0);
		expect(tabConfiguratorGeneral.findAll('#ineligible-hide-tabs').length).to.equal(0);
	});
});
